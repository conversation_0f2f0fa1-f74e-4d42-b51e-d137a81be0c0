import datetime
from dotenv import dotenv_values

# Load environment variables
env_vars = dotenv_values(".env")
Username = env_vars.get("Username", "User")
Assistantname = env_vars.get("Assistantname", "Assistant")

def get_current_time():
    """Get current time in 12-hour format with AM/PM"""
    now = datetime.datetime.now()
    return now.strftime("%I:%M %p")

def get_current_date():
    """Get current date in a readable format"""
    now = datetime.datetime.now()
    return now.strftime("%A, %B %d, %Y")

def get_current_day():
    """Get current day of the week"""
    now = datetime.datetime.now()
    return now.strftime("%A")

def get_current_month():
    """Get current month"""
    now = datetime.datetime.now()
    return now.strftime("%B")

def get_current_year():
    """Get current year"""
    now = datetime.datetime.now()
    return str(now.year)

def get_full_datetime():
    """Get complete date and time information"""
    now = datetime.datetime.now()
    return now.strftime("%A, %B %d, %Y at %I:%M %p")

def get_timezone_info():
    """Get timezone information"""
    now = datetime.datetime.now()
    return now.strftime("%Z %z") if now.strftime("%Z") else "Local Time"

def get_week_number():
    """Get current week number of the year"""
    now = datetime.datetime.now()
    return f"Week {now.isocalendar()[1]} of {now.year}"

def get_day_of_year():
    """Get current day number of the year"""
    now = datetime.datetime.now()
    day_of_year = now.timetuple().tm_yday
    return f"Day {day_of_year} of {now.year}"

def get_days_until_weekend():
    """Get number of days until weekend"""
    now = datetime.datetime.now()
    days_until_saturday = (5 - now.weekday()) % 7
    if days_until_saturday == 0:
        return "Today is Saturday!"
    elif now.weekday() == 6:  # Sunday
        return "Today is Sunday!"
    else:
        return f"{days_until_saturday} days until weekend"

def get_season():
    """Get current season"""
    now = datetime.datetime.now()
    month = now.month
    day = now.day

    if (month == 12 and day >= 21) or month in [1, 2] or (month == 3 and day < 20):
        return "Winter"
    elif (month == 3 and day >= 20) or month in [4, 5] or (month == 6 and day < 21):
        return "Spring"
    elif (month == 6 and day >= 21) or month in [7, 8] or (month == 9 and day < 22):
        return "Summer"
    else:
        return "Autumn"

def process_datetime_query(query):
    """Process various date/time related queries and return appropriate response"""
    query_lower = query.lower().strip()

    # Check for combined date and time queries first (most specific)
    if any(phrase in query_lower for phrase in ["date and time", "current date and time", "full date", "complete"]):
        return f"Right now it is {get_full_datetime()}."

    # Day-related queries (check before date queries to handle "what day is today?")
    elif any(phrase in query_lower for phrase in ["what day is today", "what day is it", "day of the week", "day today"]):
        current_day = get_current_day()
        return f"Today is {current_day}."

    # Day of year queries
    elif "day" in query_lower and "year" in query_lower:
        return f"Today is {get_day_of_year()}."

    # Time-only queries (but not if date is also mentioned)
    elif any(word in query_lower for word in ["time", "clock"]) and not any(word in query_lower for word in ["date"]):
        current_time = get_current_time()
        return f"The current time is {current_time}."

    # Date-only queries (but not if time is also mentioned and not day queries)
    elif any(word in query_lower for word in ["date", "today"]) and not any(word in query_lower for word in ["time", "clock"]) and not any(phrase in query_lower for phrase in ["day is today", "day today"]):
        current_date = get_current_date()
        return f"Today is {current_date}."

    # Month-related queries
    elif "month" in query_lower and not any(word in query_lower for word in ["date", "time", "day"]):
        current_month = get_current_month()
        return f"The current month is {current_month}."

    # Year-related queries
    elif "year" in query_lower and not any(word in query_lower for word in ["date", "time", "day", "month", "week"]):
        current_year = get_current_year()
        return f"The current year is {current_year}."

    # Week-related queries
    elif "week" in query_lower:
        if "weekend" in query_lower:
            return get_days_until_weekend()
        else:
            return f"This is {get_week_number()}."

    # Season-related queries
    elif "season" in query_lower:
        current_season = get_season()
        return f"The current season is {current_season}."

    # Timezone queries
    elif any(word in query_lower for word in ["timezone", "time zone"]):
        timezone_info = get_timezone_info()
        return f"The timezone information is: {timezone_info}."

    # General "now" or "current" queries
    elif any(word in query_lower for word in ["now", "current"]) and not any(word in query_lower for word in ["time", "date", "day", "month", "year"]):
        return f"Right now it is {get_full_datetime()}."

    # Default comprehensive response for unclear queries
    else:
        return f"Right now it is {get_full_datetime()}."

def is_datetime_query(query):
    """Check if a query is related to date/time"""
    datetime_keywords = [
        "time", "date", "day", "month", "year", "week", "today", "now", "current",
        "clock", "calendar", "weekday", "weekend", "season", "timezone", "when"
    ]

    query_lower = query.lower()
    return any(keyword in query_lower for keyword in datetime_keywords)

# Test function
if __name__ == "__main__":
    test_queries = [
        "what time is it?",
        "what's the date today?",
        "what day is it?",
        "what month is this?",
        "what year is it?",
        "what's the current date and time?",
        "tell me the time",
        "what's today's date?",
        "what day of the week is it?",
        "how many days until weekend?",
        "what season is it?",
        "what week of the year is this?"
    ]

    print("Testing DateTime module:")
    print("=" * 50)

    for query in test_queries:
        print(f"Query: {query}")
        print(f"Response: {process_datetime_query(query)}")
        print("-" * 30)
