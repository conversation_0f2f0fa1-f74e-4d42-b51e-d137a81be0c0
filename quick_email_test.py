#!/usr/bin/env python3
"""
Quick test for the improved email automation with timeout.
"""

import sys
import os

# Add the current directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def quick_test():
    """Quick test of the email function with timeout"""
    print("🧪 Quick Email Automation Test")
    print("=" * 40)
    
    try:
        from Backend.Automation import SendEmail
        
        print("📧 Testing email function with timeout...")
        print("⏱️  Maximum wait time: 45 seconds")
        print()
        
        # Test with a safe email address
        test_email = "<EMAIL>"
        test_subject = "Matrix AI Quick Test"
        test_body = "This is a quick test of the improved email automation."
        
        print(f"Recipient: {test_email}")
        print(f"Subject: {test_subject}")
        print(f"Body: {test_body}")
        print()
        print("🚀 Starting automation...")
        
        # Call the SendEmail function
        success, message = SendEmail(test_email, test_subject, test_body)
        
        print()
        print("📊 RESULTS:")
        print(f"Success: {success}")
        print(f"Message: {message}")
        
        if success:
            if "sent successfully" in message.lower():
                print("🎉 Email was automatically sent!")
            else:
                print("📋 Compose window opened for manual sending")
        else:
            print("❌ Email automation failed")
        
        return success
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    quick_test()
