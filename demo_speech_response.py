#!/usr/bin/env python3
"""
Demonstration script for Matrix AI speech-to-response functionality.
This script shows the complete flow working: speech input → AI processing → voice response.
"""

import sys
import os
import time

# Add the current directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def demonstrate_speech_to_response():
    """Demonstrate the complete speech-to-response functionality"""
    print("🤖 Matrix AI Speech-to-Response Demonstration")
    print("=" * 60)
    print()
    
    try:
        from main import MainExecution
        from Frontend.GUI import SetMicrophoneStatus, GetMicrophoneStatus, SetAssistantStatus
        
        print("🎯 This demonstration shows the complete speech-to-response flow:")
        print("   1. 🎤 You speak to the AI")
        print("   2. 🧠 AI processes your speech")
        print("   3. 💭 AI generates a response")
        print("   4. 🔊 AI speaks the response back to you")
        print()
        
        # Check microphone status
        original_status = GetMicrophoneStatus()
        print(f"📊 Current microphone status: {original_status}")
        
        if original_status.lower() != "true":
            print("🔧 Activating microphone for demonstration...")
            SetMicrophoneStatus("True")
        
        print("\n🎤 DEMONSTRATION READY")
        print("📋 Try saying one of these examples:")
        print("   • 'Hello Matrix AI'")
        print("   • 'What time is it?'")
        print("   • 'Tell me about yourself'")
        print("   • 'How are you today?'")
        print()
        
        input("Press Enter when you're ready to speak...")
        
        print("\n🗣️ LISTENING... Please speak clearly now!")
        print("(The AI will process your speech and respond)")
        
        SetAssistantStatus("Demo - Listening...")
        
        start_time = time.time()
        result = MainExecution()
        end_time = time.time()
        
        print(f"\n⏱️ Total interaction time: {end_time - start_time:.2f} seconds")
        
        # Restore original microphone status
        if original_status.lower() != "true":
            SetMicrophoneStatus(original_status)
            print(f"🔧 Microphone status restored to: {original_status}")
        
        if result:
            print("\n✅ DEMONSTRATION SUCCESSFUL!")
            print("🎉 The speech-to-response functionality is working correctly!")
            print()
            print("📋 What happened:")
            print("   ✅ Your speech was recognized")
            print("   ✅ AI processed your request")
            print("   ✅ AI generated an appropriate response")
            print("   ✅ AI spoke the response back to you")
            return True
        else:
            print("\n❌ DEMONSTRATION FAILED")
            print("⚠️ There was an issue with the speech-to-response flow")
            return False
        
    except Exception as e:
        print(f"\n❌ Error during demonstration: {e}")
        import traceback
        traceback.print_exc()
        return False

def show_system_status():
    """Show the current system status"""
    print("\n📊 System Status Check")
    print("=" * 40)
    
    try:
        from Frontend.GUI import GetMicrophoneStatus, GetAssistantStatus
        
        mic_status = GetMicrophoneStatus()
        ai_status = GetAssistantStatus()
        
        print(f"🎤 Microphone Status: {mic_status}")
        print(f"🤖 Assistant Status: {ai_status}")
        
        # Check if speech recognition is available
        try:
            from Backend.SpeechToText import SpeechRecognition
            print("✅ Speech Recognition: Available")
        except Exception as e:
            print(f"❌ Speech Recognition: Error - {e}")
        
        # Check if text-to-speech is available
        try:
            from Backend.TextToSpeech import text_to_speech
            print("✅ Text-to-Speech: Available")
        except Exception as e:
            print(f"❌ Text-to-Speech: Error - {e}")
        
        # Check if AI model is available
        try:
            from Backend.Model import FirstLayerDMM
            print("✅ AI Model: Available")
        except Exception as e:
            print(f"❌ AI Model: Error - {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error checking system status: {e}")
        return False

def main():
    """Main demonstration function"""
    print("🤖 Matrix AI Speech-to-Response Demo")
    print("=" * 50)
    print()
    
    # Show system status first
    print("🔍 Checking system status...")
    status_ok = show_system_status()
    
    if not status_ok:
        print("\n⚠️ System status check failed. Please check the errors above.")
        return
    
    print("\n" + "=" * 50)
    print("🚀 Starting Speech-to-Response Demonstration")
    print("=" * 50)
    
    # Run the demonstration
    success = demonstrate_speech_to_response()
    
    print("\n" + "=" * 50)
    print("📊 DEMONSTRATION SUMMARY")
    print("=" * 50)
    
    if success:
        print("🎉 SUCCESS! The Matrix AI speech-to-response system is working!")
        print()
        print("✅ Confirmed working components:")
        print("   • Speech recognition (converts your voice to text)")
        print("   • AI processing (understands and processes requests)")
        print("   • Response generation (creates appropriate responses)")
        print("   • Text-to-speech (speaks responses back to you)")
        print()
        print("🎯 The AI is ready to respond to your voice commands!")
    else:
        print("❌ The demonstration encountered issues.")
        print("🔧 Please check the error messages above for troubleshooting.")
    
    print("\n🎤 To use the AI normally:")
    print("   1. Run the main Matrix AI application")
    print("   2. Click the microphone button to activate listening")
    print("   3. Speak your commands clearly")
    print("   4. Listen to the AI's responses")

if __name__ == "__main__":
    main()
