<!DOCTYPE html>
<html lang="en">
<head>
    <title>Speech Recognition</title>
    <style>
        body { font-family: Arial, sans-serif; }
        #status { color: green; margin-top: 10px; }
        #error { color: red; margin-top: 10px; }
    </style>
</head>
<body>
    <button id="start" onclick="startRecognition()">Start Recognition</button>
    <button id="end" onclick="stopRecognition()">Stop Recognition</button>
    <p id="output"></p>
    <p id="status"></p>
    <p id="error"></p>
    <script>
        const output = document.getElementById('output');
        const status = document.getElementById('status');
        const errorDisplay = document.getElementById('error');
        let recognition;
        let recognitionActive = false;

        function startRecognition() {
            try {
                if (recognitionActive) {
                    stopRecognition();
                }

                recognition = new (window.SpeechRecognition || window.webkitSpeechRecognition)();
                recognition.lang = 'hi';
                recognition.continuous = false;  // Changed to false for better stability
                recognition.interimResults = false;

                recognition.onstart = function() {
                    recognitionActive = true;
                    status.textContent = "Listening...";
                    errorDisplay.textContent = "";
                };

                recognition.onresult = function(event) {
                    const transcript = event.results[event.results.length - 1][0].transcript;
                    output.textContent += transcript;
                    status.textContent = "Speech recognized!";
                };

                recognition.onerror = function(event) {
                    errorDisplay.textContent = "Error: " + event.error;
                    if (event.error === 'no-speech') {
                        // Restart on no-speech error
                        setTimeout(() => {
                            if (recognitionActive) {
                                recognition.stop();
                                recognition.start();
                            }
                        }, 300);
                    }
                };

                recognition.onend = function() {
                    if (recognitionActive && output.textContent.trim() === "") {
                        // If nothing was recognized, restart
                        setTimeout(() => {
                            if (recognitionActive) {
                                recognition.start();
                            }
                        }, 300);
                    } else {
                        status.textContent = "Recognition ended";
                    }
                };

                recognition.start();
            } catch (e) {
                errorDisplay.textContent = "Failed to start recognition: " + e.message;
            }
        }

        function stopRecognition() {
            if (recognition) {
                recognitionActive = false;
                try {
                    recognition.stop();
                } catch (e) {
                    errorDisplay.textContent = "Error stopping recognition: " + e.message;
                }
            }
            status.textContent = "Recognition stopped";
        }
    </script>
</body>
</html>