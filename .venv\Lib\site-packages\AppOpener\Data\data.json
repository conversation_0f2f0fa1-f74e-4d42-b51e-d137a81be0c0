{"name": "AppID", "about java": "Microsoft.AutoGenerated.{1E70F94B-0262-DC94-9D7C-1046A10F7BAF}", "bluestacks": "BlueStacks_nxt", "bluestacks multi-instance manager": "{6D809377-6AF0-444B-8957-A3773F02200E}\\BlueStacks_nxt\\HD-MultiInstanceManager.exe", "bluestacks services": "com.bluestacks.services", "bluestacks store": "{7C5A40EF-A0FB-4BFC-874A-C0F2E0B9FA8E}\\BlueStacks X\\BlueStacks X.exe", "calculator": "Microsoft.WindowsCalculator_8wekyb3d8bbwe!App", "camera": "Microsoft.WindowsCamera_8wekyb3d8bbwe!App", "capcut": "d:.capcut.6.1.2.2338.capcut.exe", "character map": "{1AC14E77-02E7-4E5D-B744-2EB1AE5198B7}\\charmap.exe", "check for updates": "Microsoft.AutoGenerated.{9F7343FE-06A1-E7AB-25A5-73D51C27D69E}", "click to do preview": "MicrosoftWindows.Client.CoreAI_cw5n1h2txyewy!ClickToDoApp", "clock": "Microsoft.WindowsAlarms_8wekyb3d8bbwe!App", "command prompt": "{1AC14E77-02E7-4E5D-B744-2EB1AE5198B7}\\cmd.exe", "commandline r": "{6D809377-6AF0-444B-8957-A3773F02200E}\\Maxon Cinema 4D R21\\Commandline.exe", "component services": "{1AC14E77-02E7-4E5D-B744-2EB1AE5198B7}\\comexp.msc", "computer management": "Microsoft.AutoGenerated.{8ABD94FB-E7D6-84A6-A997-C918EDDE0AE5}", "configure java": "{6D809377-6AF0-444B-8957-A3773F02200E}\\Java\\jre1.8.0_451\\bin\\javacpl.exe", "console rar manual": "{6D809377-6AF0-444B-8957-A3773F02200E}\\WinRAR\\Rar.txt", "control panel": "Microsoft.Windows.ControlPanel", "copilot": "Microsoft.Copilot_8wekyb3d8bbwe!App", "defragment and optimise drives": "{1AC14E77-02E7-4E5D-B744-2EB1AE5198B7}\\dfrgui.exe", "dev home": "Microsoft.Windows.DevHome_8wekyb3d8bbwe!App", "disk clean-up": "{1AC14E77-02E7-4E5D-B744-2EB1AE5198B7}\\cleanmgr.exe", "documentation": "{6D809377-6AF0-444B-8957-A3773F02200E}\\VideoLAN\\VLC\\Documentation.url", "event viewer": "Microsoft.AutoGenerated.{BB044BFD-25B7-2FAA-22A8-6371A93E0456}", "feedback hub": "Microsoft.WindowsFeedbackHub_8wekyb3d8bbwe!App", "file explorer": "Microsoft.Windows.Explorer", "game bar": "Microsoft.XboxGamingOverlay_8wekyb3d8bbwe!App", "get help": "Microsoft.GetHelp_8wekyb3d8bbwe!App", "get started": "MicrosoftWindows.Client.CBS_cw5n1h2txyewy!WebExperienceHost", "google chrome": "Chrome", "grabber help": "{7C5A40EF-A0FB-4BFC-874A-C0F2E0B9FA8E}\\Internet Download Manager\\grabber.chm", "grand theft auto iv l": "D:\\Grand Theft Auto IV\\Run_GTAIV.exe", "grand theft auto v enhanced l": "D:\\GTA V\\Run_GTAV.exe", "grand theft auto v legacy l": "Microsoft.AutoGenerated.{C5298DEE-B62B-3279-2A37-EACEA48EB0B2}", "hyper-v manager": "Microsoft.AutoGenerated.{7DFC4B93-681D-7B96-03E1-AE81FB7E597A}", "hyper-v quick create": "{6D809377-6AF0-444B-8957-A3773F02200E}\\Hyper-V\\VMCreate.exe", "idle python -bit": "Microsoft.AutoGenerated.{A415B597-67AD-34B9-EA07-AB178E164001}", "idm help": "{7C5A40EF-A0FB-4BFC-874A-C0F2E0B9FA8E}\\Internet Download Manager\\idman.chm", "install additional tools for node js": "Microsoft.AutoGenerated.{04770C2D-34E6-BE94-EFAD-FA817237D6E9}", "intel graphics command center": "AppUp.IntelGraphicsExperience_8j3eq9eme6ctt!App", "internet download manager": "{7C5A40EF-A0FB-4BFC-874A-C0F2E0B9FA8E}\\Internet Download Manager\\IDMan.exe", "iscsi initiator": "{1AC14E77-02E7-4E5D-B744-2EB1AE5198B7}\\iscsicpl.exe", "license": "{7C5A40EF-A0FB-4BFC-874A-C0F2E0B9FA8E}\\Internet Download Manager\\license.txt", "license english": "{6D809377-6AF0-444B-8957-A3773F02200E}\\Oracle\\VirtualBox\\License_en_US.rtf", "live captions": "{1AC14E77-02E7-4E5D-B744-2EB1AE5198B7}\\LiveCaptions.exe", "local security policy": "Microsoft.AutoGenerated.{BD3F924E-55FB-A1BA-9DE6-B50F9F2460AC}", "magnifier": "{1AC14E77-02E7-4E5D-B744-2EB1AE5198B7}\\magnify.exe", "maxon cinema d r": "{6D809377-6AF0-444B-8957-A3773F02200E}\\Maxon Cinema 4D R21\\Cinema 4D.exe", "media player": "Microsoft.ZuneMusic_8wekyb3d8bbwe!Microsoft.ZuneMusic", "microsoft copilot": "Microsoft.MicrosoftOfficeHub_8wekyb3d8bbwe!Microsoft.MicrosoftOfficeHub", "microsoft clipchamp": "Clipchamp.Clipchamp_yxz26nhyzhsrt!App", "microsoft edge": "MSEdge", "microsoft store": "Microsoft.WindowsStore_8wekyb3d8bbwe!App", "microsoft teams": "MSTeams_8wekyb3d8bbwe!MSTeams", "microsoft to do": "Microsoft.Todos_8wekyb3d8bbwe!App", "narrator": "{1AC14E77-02E7-4E5D-B744-2EB1AE5198B7}\\narrator.exe", "news": "Microsoft.BingNews_8wekyb3d8bbwe!AppexNews", "node js": "{6D809377-6AF0-444B-8957-A3773F02200E}\\nodejs\\node.exe", "node js command prompt": "Microsoft.AutoGenerated.{499EE7CA-7ED5-EBC9-AB09-80DC143D7B90}", "node js documentation": "https://nodejs.org/download/release/v22.15.1/docs/api/", "node js website": "https://nodejs.org/", "notepad": "Microsoft.WindowsNotepad_8wekyb3d8bbwe!App", "odbc data sources -bit": "{1AC14E77-02E7-4E5D-B744-2EB1AE5198B7}\\odbcad32.exe", "on-screen keyboard": "{1AC14E77-02E7-4E5D-B744-2EB1AE5198B7}\\osk.exe", "oracle virtualbox": "{6D809377-6AF0-444B-8957-A3773F02200E}\\Oracle\\VirtualBox\\VirtualBox.exe", "outlook": "Microsoft.OutlookForWindows_8wekyb3d8bbwe!Microsoft.OutlookforWindows", "paint": "Microsoft.Paint_8wekyb3d8bbwe!App", "performance monitor": "Microsoft.AutoGenerated.{8AA47365-B2B3-1961-69EB-F866E376B12F}", "phone link": "Microsoft.YourPhone_8wekyb3d8bbwe!App", "photos": "Microsoft.Windows.Photos_8wekyb3d8bbwe!App", "power automate": "Microsoft.PowerAutomateDesktop_8wekyb3d8bbwe!PAD.Console", "print management": "{1AC14E77-02E7-4E5D-B744-2EB1AE5198B7}\\printmanagement.msc", "python -bit": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\python.exe", "python manuals -bit": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Doc\\html\\index.html", "python module docs -bit": "Microsoft.AutoGenerated.{8FCA0E64-721D-C624-B3BB-21D5DD370D08}", "quick assist": "MicrosoftCorporationII.QuickAssist_8wekyb3d8bbwe!App", "recovery drive": "{1AC14E77-02E7-4E5D-B744-2EB1AE5198B7}\\RecoveryDrive.exe", "registry editor": "{F38BF404-1D43-42F2-9305-67DE0B28FC23}\\regedit.exe", "release notes": "{6D809377-6AF0-444B-8957-A3773F02200E}\\VideoLAN\\VLC\\NEWS.txt", "remote desktop connection": "Microsoft.Windows.RemoteDesktop", "resource monitor": "Microsoft.AutoGenerated.{C804BBA7-FA5F-CBF7-8B55-2096E5F972CB}", "run": "Microsoft.Windows.Shell.RunDialog", "services": "{1AC14E77-02E7-4E5D-B744-2EB1AE5198B7}\\services.msc", "settings": "windows.immersivecontrolpanel_cw5n1h2txyewy!microsoft.windows.immersivecontrolpanel", "snipping tool": "Microsoft.ScreenSketch_8wekyb3d8bbwe!App", "solitaire & casual games": "Microsoft.MicrosoftSolitaireCollection_8wekyb3d8bbwe!App", "sound recorder": "Microsoft.WindowsSoundRecorder_8wekyb3d8bbwe!App", "steps recorder": "{1AC14E77-02E7-4E5D-B744-2EB1AE5198B7}\\psr.exe", "sticky notes": "Microsoft.MicrosoftStickyNotes_8wekyb3d8bbwe!App", "system configuration": "{1AC14E77-02E7-4E5D-B744-2EB1AE5198B7}\\msconfig.exe", "system information": "{1AC14E77-02E7-4E5D-B744-2EB1AE5198B7}\\msinfo32.exe", "task manager": "Microsoft.AutoGenerated.{923DD477-5846-686B-A659-0FCCD73851A8}", "task scheduler": "Microsoft.AutoGenerated.{C1C6F8AC-40A3-0F5C-146F-65A9DC70BBB4}", "team render client r": "{6D809377-6AF0-444B-8957-A3773F02200E}\\Maxon Cinema 4D R21\\Cinema 4D Team Render Client.exe", "team render server r": "{6D809377-6AF0-444B-8957-A3773F02200E}\\Maxon Cinema 4D R21\\Cinema 4D Team Render Server.exe", "terminal": "Microsoft.WindowsTerminal_8wekyb3d8bbwe!App", "tlauncher": "C:\\Users\\<USER>\\AppData\\Roaming\\.minecraft\\TLauncher.exe", "tutorials": "{7C5A40EF-A0FB-4BFC-874A-C0F2E0B9FA8E}\\Internet Download Manager\\tutor.chm", "uninstall idm": "{7C5A40EF-A0FB-4BFC-874A-C0F2E0B9FA8E}\\Internet Download Manager\\Uninstall.exe", "uninstall node js": "Microsoft.AutoGenerated.{F8497045-BE0F-20C8-9200-40508C6BBF52}", "user manual pdf english": "{6D809377-6AF0-444B-8957-A3773F02200E}\\Oracle\\VirtualBox\\doc\\UserManual.pdf", "videolan website": "{6D809377-6AF0-444B-8957-A3773F02200E}\\VideoLAN\\VLC\\VideoLAN Website.url", "visit java com": "https://java.com/", "vlc media player": "{6D809377-6AF0-444B-8957-A3773F02200E}\\VideoLAN\\VLC\\vlc.exe", "vlc media player - reset preferences and cache files microsoft autogenerated -ae a- fc-a - cc a": "}", "vlc media player skinned": "Microsoft.AutoGenerated.{30BD9A02-CB9A-93FD-A859-09C8803F2346}", "voice access": "{1AC14E77-02E7-4E5D-B744-2EB1AE5198B7}\\voiceaccess.exe", "weather": "Microsoft.BingWeather_8wekyb3d8bbwe!App", "what is new in the latest version": "{6D809377-6AF0-444B-8957-A3773F02200E}\\WinRAR\\WhatsNew.txt", "whatsapp": "5319275A.WhatsAppDesktop_cv1g1gvanyjgm!App", "windows back up": "MicrosoftWindows.Client.CBS_cw5n1h2txyewy!WindowsBackup", "windows defender firewall with advanced security": "{1AC14E77-02E7-4E5D-B744-2EB1AE5198B7}\\WF.msc", "windows media player legacy": "Microsoft.Windows.MediaPlayer32", "windows memory diagnostic": "{1AC14E77-02E7-4E5D-B744-2EB1AE5198B7}\\MdSched.exe", "windows powershell": "{1AC14E77-02E7-4E5D-B744-2EB1AE5198B7}\\WindowsPowerShell\\v1.0\\powershell.exe", "windows powershell x": "{D65231B0-B2F1-4857-A4CE-A8E7C6EA7D27}\\WindowsPowerShell\\v1.0\\powershell.exe", "windows powershell ise": "{1AC14E77-02E7-4E5D-B744-2EB1AE5198B7}\\WindowsPowerShell\\v1.0\\PowerShell_ISE.exe", "windows powershell ise x": "{D65231B0-B2F1-4857-A4CE-A8E7C6EA7D27}\\WindowsPowerShell\\v1.0\\PowerShell_ISE.exe", "windows security": "Microsoft.SecHealthUI_8wekyb3d8bbwe!SecHealthUI", "windows tools": "Microsoft.Windows.AdministrativeTools", "winrar": "{6D809377-6AF0-444B-8957-A3773F02200E}\\WinRAR\\WinRAR.exe", "winrar help": "{6D809377-6AF0-444B-8957-A3773F02200E}\\WinRAR\\WinRAR.chm", "zoom workplace": "zoom.us.Zoom Video Meetings"}