#!/usr/bin/env python3
"""
Test script for emotion analysis functionality.
This script tests the emotion analysis module without requiring heavy dependencies.
"""

import os
import sys
import time
import numpy as np

# Add the current directory to Python path
sys.path.insert(0, os.getcwd())

def test_emotion_analysis():
    """Test the emotion analysis functionality."""
    print("🧪 Testing Emotion Analysis Module...")
    print("=" * 50)
    
    try:
        # Import emotion analysis modules
        from Backend.EmotionAnalysis import (
            analyze_voice_emotion,
            get_current_emotion,
            get_emotion_history,
            set_emotion_performance_mode,
            get_emotion_performance_stats,
            clear_emotion_cache
        )
        
        print("✅ Emotion analysis modules imported successfully")
        
        # Test performance modes
        print("\n🚀 Testing performance modes...")
        for mode in ["fast", "balanced", "accurate"]:
            set_emotion_performance_mode(mode)
            stats = get_emotion_performance_stats()
            print(f"  {mode.upper()} mode: interval={stats['min_analysis_interval']}, cache={stats['cache_enabled']}")
        
        # Set back to fast mode for testing
        set_emotion_performance_mode("fast")
        
        # Generate test audio data
        print("\n🎵 Generating test audio patterns...")
        sample_rate = 16000
        duration = 1  # 1 second
        t = np.linspace(0, duration, sample_rate * duration)
        
        test_cases = [
            ("High energy (excited/happy)", np.sin(2 * np.pi * 300 * t) * 0.8 + np.random.normal(0, 0.1, len(t))),
            ("Low energy (sad/calm)", np.sin(2 * np.pi * 150 * t) * 0.2 + np.random.normal(0, 0.05, len(t))),
            ("Variable energy (angry)", np.sin(2 * np.pi * 200 * t) * np.random.random(len(t)) * 0.7),
            ("Medium energy (neutral)", np.sin(2 * np.pi * 180 * t) * 0.4 + np.random.normal(0, 0.08, len(t))),
            ("Very high energy (excited)", np.sin(2 * np.pi * 400 * t) * 0.9 + np.random.normal(0, 0.15, len(t)))
        ]
        
        print("\n🔍 Analyzing emotions from test audio...")
        results = []
        
        for i, (description, audio_data) in enumerate(test_cases, 1):
            print(f"\n  Test {i}: {description}")
            
            # Convert to int16 format (simulating real audio)
            audio_int16 = (audio_data * 32767).astype(np.int16)
            
            # Analyze emotion
            start_time = time.time()
            emotion, confidence = analyze_voice_emotion(audio_int16, sample_rate)
            analysis_time = time.time() - start_time
            
            print(f"    Result: {emotion} (confidence: {confidence:.2f})")
            print(f"    Analysis time: {analysis_time:.4f} seconds")
            
            results.append({
                "description": description,
                "emotion": emotion,
                "confidence": confidence,
                "analysis_time": analysis_time
            })
            
            # Small delay to test rate limiting
            time.sleep(0.05)
        
        # Test current emotion state
        print("\n📊 Current emotion state:")
        current = get_current_emotion()
        print(f"  Emotion: {current['emotion']}")
        print(f"  Confidence: {current['confidence']:.2f}")
        print(f"  Timestamp: {current['timestamp']}")
        
        # Test emotion history
        print("\n📈 Emotion history:")
        history = get_emotion_history(3)
        for i, record in enumerate(history[-3:], 1):
            print(f"  {i}. {record['emotion']} ({record['confidence']:.2f}) at {record['timestamp']}")
        
        # Test performance statistics
        print("\n⚡ Performance statistics:")
        stats = get_emotion_performance_stats()
        for key, value in stats.items():
            print(f"  {key}: {value}")
        
        # Test cache functionality
        print("\n🗄️ Testing cache functionality...")
        print(f"  Cache size before clear: {stats['cache_size']}")
        clear_emotion_cache()
        new_stats = get_emotion_performance_stats()
        print(f"  Cache size after clear: {new_stats['cache_size']}")
        
        # Performance summary
        print("\n📋 Performance Summary:")
        avg_time = sum(r["analysis_time"] for r in results) / len(results)
        print(f"  Average analysis time: {avg_time:.4f} seconds")
        print(f"  Total tests: {len(results)}")
        print(f"  All tests completed successfully: ✅")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("   Make sure all required modules are available")
        return False
    except Exception as e:
        print(f"❌ Test error: {e}")
        return False

def test_emotion_manager():
    """Test the emotion manager functionality."""
    print("\n🧪 Testing Emotion Manager Module...")
    print("=" * 50)
    
    try:
        from Backend.EmotionManager import (
            get_current_emotion,
            get_emotion_context,
            get_emotion_summary,
            add_emotion_record
        )
        
        print("✅ Emotion manager modules imported successfully")
        
        # Add some test records
        print("\n📝 Adding test emotion records...")
        test_emotions = [
            ("happy", 0.8, "User greeting"),
            ("neutral", 0.6, "Normal conversation"),
            ("excited", 0.9, "Positive response"),
            ("calm", 0.7, "Relaxed interaction")
        ]
        
        for emotion, confidence, context in test_emotions:
            add_emotion_record(emotion, confidence, context)
            print(f"  Added: {emotion} ({confidence}) - {context}")
            time.sleep(0.1)
        
        # Test manager functions
        print("\n📊 Testing manager functions...")
        
        current = get_current_emotion()
        print(f"  Current emotion: {current}")
        
        context = get_emotion_context()
        print(f"  Emotion context: {context}")
        
        summary = get_emotion_summary()
        print(f"  Emotion summary: {summary}")
        
        print("✅ Emotion manager tests completed successfully")
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Test error: {e}")
        return False

def test_integration():
    """Test integration with speech recognition."""
    print("\n🧪 Testing Integration with Speech Recognition...")
    print("=" * 50)
    
    try:
        # Test if VoskSpeechToText can import emotion analysis
        from Backend.VoskSpeechToText import EMOTION_ANALYSIS_AVAILABLE
        
        if EMOTION_ANALYSIS_AVAILABLE:
            print("✅ Emotion analysis is available in VoskSpeechToText")
            
            # Test emotion context functions
            from Backend.VoskSpeechToText import get_emotion_context, save_emotion_context
            
            # Test saving emotion context
            test_text = "Hello, how are you today?"
            save_emotion_context(test_text)
            print(f"✅ Saved emotion context for: '{test_text}'")
            
            # Test getting emotion context
            context = get_emotion_context()
            print(f"✅ Retrieved emotion context: {context}")
            
        else:
            print("⚠️ Emotion analysis is not available in VoskSpeechToText")
            
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Test error: {e}")
        return False

def main():
    """Run all tests."""
    print("🤖 Matrix AI - Emotion Analysis Test Suite")
    print("=" * 60)
    
    tests = [
        ("Emotion Analysis Core", test_emotion_analysis),
        ("Emotion Manager", test_emotion_manager),
        ("Integration Test", test_integration)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🔬 Running {test_name}...")
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                print(f"✅ {test_name} PASSED")
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} CRASHED: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("📋 TEST SUMMARY")
    print("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {test_name}: {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Emotion analysis is working correctly.")
        return True
    else:
        print("⚠️ Some tests failed. Check the output above for details.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
