import os
import json
import numpy as np
import threading
import time
from collections import deque
from typing import Dict, List, Optional, Tuple
import tempfile

# Lightweight emotion analysis using audio features
class EmotionAnalyzer:
    """
    Lightweight emotion analysis from voice using audio signal processing.
    Uses basic audio features like energy, pitch, and spectral characteristics.
    """
    
    def __init__(self):
        self.emotion_history = deque(maxlen=10)
        self.current_emotion = "neutral"
        self.confidence = 0.0
        self.emotion_cache = {}
        self.analysis_enabled = True

        # Speed optimization settings
        self.cache_enabled = True
        self.cache_max_size = 100
        self.min_analysis_interval = 0.1  # Minimum time between analyses (seconds)
        self.last_analysis_time = 0
        self.skip_low_energy_audio = True
        self.energy_threshold_for_analysis = 0.05
        
        # Emotion mapping based on audio features
        self.emotion_features = {
            "happy": {"energy_range": (0.6, 1.0), "pitch_range": (1.1, 1.5), "variability": (0.3, 0.8)},
            "sad": {"energy_range": (0.1, 0.4), "pitch_range": (0.6, 0.9), "variability": (0.1, 0.3)},
            "angry": {"energy_range": (0.7, 1.0), "pitch_range": (1.2, 1.8), "variability": (0.5, 1.0)},
            "calm": {"energy_range": (0.2, 0.6), "pitch_range": (0.8, 1.2), "variability": (0.1, 0.4)},
            "excited": {"energy_range": (0.8, 1.0), "pitch_range": (1.3, 1.7), "variability": (0.6, 1.0)},
            "neutral": {"energy_range": (0.3, 0.7), "pitch_range": (0.9, 1.1), "variability": (0.2, 0.5)}
        }
        
        # Initialize data storage
        self.data_dir = os.path.join(os.getcwd(), "Data")
        os.makedirs(self.data_dir, exist_ok=True)
        self.emotion_file = os.path.join(self.data_dir, "EmotionData.json")
        
        # Load previous emotion data
        self.load_emotion_data()
    
    def calculate_audio_features(self, audio_data: np.ndarray, sample_rate: int = 16000) -> Dict[str, float]:
        """Calculate basic audio features for emotion analysis."""
        try:
            # Ensure audio_data is numpy array
            if isinstance(audio_data, bytes):
                audio_data = np.frombuffer(audio_data, dtype=np.int16)
            
            # Normalize audio
            if len(audio_data) == 0:
                return {"energy": 0.0, "pitch_factor": 1.0, "variability": 0.0}
            
            audio_data = audio_data.astype(np.float32) / 32768.0
            
            # Calculate energy (RMS)
            energy = np.sqrt(np.mean(np.square(audio_data)))
            
            # Calculate pitch factor using zero crossing rate
            zero_crossings = np.sum(np.diff(np.sign(audio_data)) != 0)
            pitch_factor = zero_crossings / len(audio_data) * sample_rate / 2
            pitch_factor = pitch_factor / 200.0  # Normalize to typical speech range
            
            # Calculate variability (standard deviation of energy in chunks)
            chunk_size = len(audio_data) // 10 if len(audio_data) > 10 else 1
            chunks = [audio_data[i:i+chunk_size] for i in range(0, len(audio_data), chunk_size)]
            chunk_energies = [np.sqrt(np.mean(np.square(chunk))) for chunk in chunks if len(chunk) > 0]
            variability = np.std(chunk_energies) if len(chunk_energies) > 1 else 0.0
            
            return {
                "energy": min(energy * 2, 1.0),  # Scale and cap at 1.0
                "pitch_factor": min(pitch_factor, 2.0),  # Cap at 2.0
                "variability": min(variability * 5, 1.0)  # Scale and cap at 1.0
            }
            
        except Exception as e:
            print(f"Error calculating audio features: {e}")
            return {"energy": 0.0, "pitch_factor": 1.0, "variability": 0.0}
    
    def analyze_emotion(self, audio_data: np.ndarray, sample_rate: int = 16000) -> Tuple[str, float]:
        """Analyze emotion from audio data with speed optimizations."""
        if not self.analysis_enabled:
            return "neutral", 0.5

        # Speed optimization: Rate limiting
        current_time = time.time()
        if current_time - self.last_analysis_time < self.min_analysis_interval:
            return self.current_emotion, self.confidence

        try:
            # Calculate audio features
            features = self.calculate_audio_features(audio_data, sample_rate)

            # Speed optimization: Skip analysis for very low energy audio
            if self.skip_low_energy_audio and features["energy"] < self.energy_threshold_for_analysis:
                return self.current_emotion, max(self.confidence * 0.9, 0.1)  # Decay confidence slightly

            # Speed optimization: Check cache for similar features
            if self.cache_enabled:
                cache_key = self._get_cache_key(features)
                if cache_key in self.emotion_cache:
                    cached_result = self.emotion_cache[cache_key]
                    self.last_analysis_time = current_time
                    return cached_result["emotion"], cached_result["confidence"]
            
            # Score each emotion based on feature matching
            emotion_scores = {}
            
            for emotion, ranges in self.emotion_features.items():
                score = 0.0
                
                # Energy score
                energy = features["energy"]
                energy_min, energy_max = ranges["energy_range"]
                if energy_min <= energy <= energy_max:
                    score += 1.0
                else:
                    score += max(0, 1.0 - abs(energy - (energy_min + energy_max) / 2) * 2)
                
                # Pitch score
                pitch = features["pitch_factor"]
                pitch_min, pitch_max = ranges["pitch_range"]
                if pitch_min <= pitch <= pitch_max:
                    score += 1.0
                else:
                    score += max(0, 1.0 - abs(pitch - (pitch_min + pitch_max) / 2) * 2)
                
                # Variability score
                variability = features["variability"]
                var_min, var_max = ranges["variability"]
                if var_min <= variability <= var_max:
                    score += 1.0
                else:
                    score += max(0, 1.0 - abs(variability - (var_min + var_max) / 2) * 2)
                
                emotion_scores[emotion] = score / 3.0  # Average of three features
            
            # Find best matching emotion
            best_emotion = max(emotion_scores, key=emotion_scores.get)
            confidence = emotion_scores[best_emotion]
            
            # Apply smoothing with previous emotions
            if len(self.emotion_history) > 0:
                recent_emotions = [e["emotion"] for e in list(self.emotion_history)[-3:]]
                if recent_emotions.count(best_emotion) >= 2:
                    confidence = min(confidence + 0.1, 1.0)  # Boost confidence for consistent emotions
            
            # Update history
            emotion_data = {
                "emotion": best_emotion,
                "confidence": confidence,
                "timestamp": time.time(),
                "features": features
            }
            self.emotion_history.append(emotion_data)
            
            # Update current emotion
            self.current_emotion = best_emotion
            self.confidence = confidence

            # Speed optimization: Cache result
            if self.cache_enabled:
                cache_key = self._get_cache_key(features)
                self.emotion_cache[cache_key] = {"emotion": best_emotion, "confidence": confidence}

                # Limit cache size
                if len(self.emotion_cache) > self.cache_max_size:
                    # Remove oldest entries (simple FIFO)
                    oldest_key = next(iter(self.emotion_cache))
                    del self.emotion_cache[oldest_key]

            # Update analysis time
            self.last_analysis_time = current_time

            # Save emotion data (less frequently for speed)
            if current_time - getattr(self, 'last_save_time', 0) > 1.0:  # Save at most once per second
                self.save_emotion_data()
                self.last_save_time = current_time

            return best_emotion, confidence
            
        except Exception as e:
            print(f"Error analyzing emotion: {e}")
            return "neutral", 0.5

    def _get_cache_key(self, features: Dict[str, float]) -> str:
        """Generate a cache key from audio features."""
        # Round features to reduce cache key variations
        energy = round(features["energy"], 2)
        pitch = round(features["pitch_factor"], 2)
        variability = round(features["variability"], 2)
        return f"{energy}_{pitch}_{variability}"
    
    def get_current_emotion(self) -> Dict[str, any]:
        """Get the current emotion state."""
        return {
            "emotion": self.current_emotion,
            "confidence": self.confidence,
            "timestamp": time.time()
        }
    
    def get_emotion_history(self, count: int = 5) -> List[Dict]:
        """Get recent emotion history."""
        return list(self.emotion_history)[-count:]
    
    def save_emotion_data(self):
        """Save emotion data to file."""
        try:
            # Convert numpy types to Python native types for JSON serialization
            def convert_numpy_types(obj):
                if isinstance(obj, np.floating):
                    return float(obj)
                elif isinstance(obj, np.integer):
                    return int(obj)
                elif isinstance(obj, np.ndarray):
                    return obj.tolist()
                elif isinstance(obj, dict):
                    return {k: convert_numpy_types(v) for k, v in obj.items()}
                elif isinstance(obj, list):
                    return [convert_numpy_types(item) for item in obj]
                return obj

            data = {
                "current_emotion": self.current_emotion,
                "confidence": float(self.confidence),
                "history": convert_numpy_types(list(self.emotion_history)),
                "last_updated": time.time()
            }
            with open(self.emotion_file, "w", encoding="utf-8") as f:
                json.dump(data, f, indent=2)
        except Exception as e:
            print(f"Error saving emotion data: {e}")
    
    def load_emotion_data(self):
        """Load emotion data from file."""
        try:
            if os.path.exists(self.emotion_file):
                with open(self.emotion_file, "r", encoding="utf-8") as f:
                    data = json.load(f)
                    self.current_emotion = data.get("current_emotion", "neutral")
                    self.confidence = data.get("confidence", 0.0)
                    # Load recent history (last 5 items)
                    history = data.get("history", [])
                    self.emotion_history = deque(history[-5:], maxlen=10)
        except Exception as e:
            print(f"Error loading emotion data: {e}")
    
    def enable_analysis(self):
        """Enable emotion analysis."""
        self.analysis_enabled = True
    
    def disable_analysis(self):
        """Disable emotion analysis."""
        self.analysis_enabled = False
    
    def reset_emotion_state(self):
        """Reset emotion state to neutral."""
        self.current_emotion = "neutral"
        self.confidence = 0.0
        self.emotion_history.clear()
        self.save_emotion_data()

    def set_performance_mode(self, mode: str):
        """Set performance optimization mode."""
        if mode == "fast":
            self.min_analysis_interval = 0.2
            self.cache_enabled = True
            self.skip_low_energy_audio = True
            self.energy_threshold_for_analysis = 0.1
        elif mode == "balanced":
            self.min_analysis_interval = 0.1
            self.cache_enabled = True
            self.skip_low_energy_audio = True
            self.energy_threshold_for_analysis = 0.05
        elif mode == "accurate":
            self.min_analysis_interval = 0.05
            self.cache_enabled = False
            self.skip_low_energy_audio = False
            self.energy_threshold_for_analysis = 0.01
        else:
            print(f"Unknown performance mode: {mode}. Using 'balanced'.")
            self.set_performance_mode("balanced")

    def clear_cache(self):
        """Clear the emotion analysis cache."""
        self.emotion_cache.clear()
        print("Emotion analysis cache cleared")

    def get_performance_stats(self) -> Dict[str, any]:
        """Get performance statistics."""
        return {
            "cache_size": len(self.emotion_cache),
            "cache_enabled": self.cache_enabled,
            "min_analysis_interval": self.min_analysis_interval,
            "skip_low_energy": self.skip_low_energy_audio,
            "energy_threshold": self.energy_threshold_for_analysis,
            "last_analysis_time": self.last_analysis_time
        }

# Global emotion analyzer instance
emotion_analyzer = EmotionAnalyzer()

def analyze_voice_emotion(audio_data, sample_rate: int = 16000) -> Tuple[str, float]:
    """
    Main function to analyze emotion from voice audio data.
    
    Args:
        audio_data: Audio data as numpy array or bytes
        sample_rate: Sample rate of the audio (default: 16000)
    
    Returns:
        Tuple of (emotion_name, confidence_score)
    """
    return emotion_analyzer.analyze_emotion(audio_data, sample_rate)

def get_current_emotion() -> Dict[str, any]:
    """Get the current detected emotion."""
    return emotion_analyzer.get_current_emotion()

def get_emotion_history(count: int = 5) -> List[Dict]:
    """Get recent emotion detection history."""
    return emotion_analyzer.get_emotion_history(count)

def enable_emotion_analysis():
    """Enable emotion analysis."""
    emotion_analyzer.enable_analysis()

def disable_emotion_analysis():
    """Disable emotion analysis."""
    emotion_analyzer.disable_analysis()

def set_emotion_performance_mode(mode: str):
    """Set emotion analysis performance mode (fast, balanced, accurate)."""
    emotion_analyzer.set_performance_mode(mode)

def clear_emotion_cache():
    """Clear emotion analysis cache."""
    emotion_analyzer.clear_cache()

def get_emotion_performance_stats() -> Dict[str, any]:
    """Get emotion analysis performance statistics."""
    return emotion_analyzer.get_performance_stats()

if __name__ == "__main__":
    # Test the emotion analyzer
    print("Testing Emotion Analyzer...")
    
    # Generate test audio data
    sample_rate = 16000
    duration = 2  # seconds
    t = np.linspace(0, duration, sample_rate * duration)
    
    # Test different audio patterns
    test_cases = [
        ("High energy, high pitch", np.sin(2 * np.pi * 300 * t) * 0.8),
        ("Low energy, low pitch", np.sin(2 * np.pi * 150 * t) * 0.2),
        ("Variable energy", np.sin(2 * np.pi * 200 * t) * np.random.random(len(t)) * 0.6),
    ]
    
    for name, audio in test_cases:
        emotion, confidence = analyze_voice_emotion(audio, sample_rate)
        print(f"{name}: {emotion} (confidence: {confidence:.2f})")
    
    print(f"\nCurrent emotion state: {get_current_emotion()}")
