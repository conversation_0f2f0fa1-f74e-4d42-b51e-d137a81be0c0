from groq import Groq
from json import load, dump
import datetime
from dotenv import dotenv_values

# Load environment variables
env_vars = dotenv_values(".env")

Username = env_vars.get("Username")
Assistantname = env_vars.get("Assistantname")
GroqAPIKey = env_vars.get("GroqAPIKey")

# Initialize Groq client
client = Groq(api_key=GroqAPIKey)

# Define system message
System = f"""Hello, I am {Username}. You are a very accurate and advanced AI chatbot named {Assistantname}, which has real-time up-to-date information from the internet.
*** Provide answers in a professional way. Make sure to add full stops, commas, question marks, and use proper grammar. ***
*** Just answer the question from the provided data in a professional way. ***"""

SystemChatBot = [
    {"role": "system", "content": System}
]

# Load previous chat messages from file (if exists)
try:
    with open(r"Data\ChatLog.json", "r") as f:
        messages = load(f)
except FileNotFoundError:
    messages = []
    with open(r"Data\ChatLog.json", "w") as f:
        dump(messages, f)

# Function to get real-time date and time information.
def RealtimeInformation():
    current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    return f"Current date and time: {current_time}"

# Function to clean up the assistant's response
def AnswerModifier(Answer):
    lines = Answer.split('\n')
    non_empty_lines = [line for line in lines if line.strip()]
    return '\n'.join(non_empty_lines)

# Main chatbot function
def ChatBot(Query):
    global messages  # Ensure we are using the global messages list

    try:
        # Load previous chat messages
        with open(r"Data\ChatLog.json", "r") as f:
            messages = load(f)

        # Append user's query
        messages.append({"role": "user", "content": Query})

        # Make API request to Groq - OPTIMIZED FOR SPEED
        completion = client.chat.completions.create(
            model="llama3-70b-8192",
            messages=SystemChatBot + [{"role": "system", "content": RealtimeInformation()}] + messages,
            max_tokens=512,  # Reduced from 1024 to 512 for faster response
            temperature=0.7,
            top_p=1,
            stream=False  # Changed to False for proper handling
        )

        Answer = completion.choices[0].message.content if completion.choices else "No response received."

        Answer = Answer.replace("</s>,", "")

        # Append AI response to chat log
        messages.append({"role": "assistant", "content": Answer})

        # Save updated messages back to file
        with open(r"Data\ChatLog.json", "w") as f:
            dump(messages, f, indent=4)

        return AnswerModifier(Answer)

    except Exception as e:
        print(f"Error: {e}")

        # Reset chat log to prevent infinite recursion
        with open(r"Data\ChatLog.json", "w") as f:
            dump([], f, indent=4)

        return "An error occurred. Please try again later."

# Run chatbot in interactive mode
if __name__ == "__main__":
    while True:
        user_input = input("Enter Your Question: ")
        if user_input.lower() in ["exit", "quit"]:
            print("Goodbye!")
            break
        print(ChatBot(user_input))
