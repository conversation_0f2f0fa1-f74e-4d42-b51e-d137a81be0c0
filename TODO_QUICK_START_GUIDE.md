# Matrix AI TO-DO List - Quick Start Guide

## 🚀 Getting Started

The Matrix AI TO-DO list feature is now fully integrated and ready to use! Simply speak your commands to Matrix AI and it will manage your tasks intelligently.

## 📝 Basic Voice Commands

### Adding Tasks
- **"remind me to [task] at [time]"**
  - Example: "remind me to call john at 5pm tomorrow"
  
- **"add task [description] deadline [date/time]"**
  - Example: "add task buy groceries deadline friday"
  
- **"set a todo to [task] by [deadline] remind me on [date]"**
  - Example: "set a todo to finish project by monday remind me on sunday"

### Viewing Tasks
- **"show my tasks"** - Display all pending tasks
- **"list my todos"** - Same as above
- **"todo overview"** - Show task summary with statistics

### Managing Tasks
- **"mark task [number] as complete"** - Complete a task
- **"remove task [number]"** - Delete a task
- **"edit task [number] change deadline to [new date]"** - Modify deadline
- **"edit task [number] [new description]"** - Change task description

## 🕐 Smart Date Recognition

Matrix AI understands natural language dates:
- **Relative**: "today", "tomorrow", "next week", "next month"
- **Day names**: "monday", "tuesday", etc. (next occurrence)
- **Specific dates**: "january 25", "25th june at 9pm"
- **Times**: "3pm", "9:00am", "18:30"

## 🔔 Automatic Reminders

- Tasks with deadlines automatically get reminders 1 hour before
- Custom reminder times can be set when creating tasks
- Matrix AI will speak and display reminders at the right time
- Background system runs continuously to monitor reminders

## 📊 Priority Levels

Tasks are automatically assigned priority levels:
- 🔴 **High Priority**: Critical tasks
- 🟡 **Medium Priority**: Normal tasks (default)
- 🟢 **Low Priority**: Optional tasks

## 💡 Usage Tips

1. **Be specific with dates**: "friday at 3pm" works better than just "friday"
2. **Use task numbers**: When editing/completing tasks, use the number shown in the list
3. **Natural language**: Speak naturally - Matrix AI understands various phrasings
4. **Check regularly**: Use "show my tasks" to stay on top of your to-dos

## 🎯 Example Conversation

**You**: "remind me to call mom at 6pm today"
**Matrix**: "Task added successfully: 'call mom' with deadline on Friday, January 31 at 06:00 PM and reminder set for Friday, January 31 at 05:00 PM"

**You**: "show my tasks"
**Matrix**: "Your pending tasks: 1. 🟡 call mom (Due: Jan 31, 06:00 PM)"

**You**: "mark task 1 as complete"
**Matrix**: "Task completed: 'call mom'. Well done!"

## 🔧 Technical Features

- **Persistent Storage**: Tasks saved to `Data/TodoList.json`
- **Background Reminders**: Continuous monitoring for due tasks
- **Cross-Platform**: Works on Windows, macOS, and Linux
- **Voice Integration**: Fully integrated with Matrix AI's speech system
- **Error Handling**: Graceful handling of invalid inputs

## 🆘 Troubleshooting

- **"Task not found"**: Check task numbers with "show my tasks"
- **Date not recognized**: Try simpler formats like "tomorrow" or "friday"
- **No reminders**: Ensure Matrix AI is running continuously
- **Tasks not saving**: Check that `Data/` directory exists and is writable

## 🎉 You're Ready!

Start using your new TO-DO list feature right away! Matrix AI will help you stay organized and never miss important tasks.

**Try it now**: Say "remind me to test the todo feature in 5 minutes" and see it in action!
