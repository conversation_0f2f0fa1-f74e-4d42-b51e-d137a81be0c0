#!/usr/bin/env python3
"""
Test script for Matrix AI email automation with voice confirmation.
This tests the complete flow: compose → voice confirmation → automatic sending.
"""

import sys
import os

# Add the current directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_voice_confirmation():
    """Test the voice confirmation functionality"""
    print("🎤 Voice Confirmation Email Test")
    print("=" * 50)
    
    try:
        from Backend.Automation import _get_voice_confirmation
        
        print("📧 Testing voice confirmation system...")
        print("⚠️  Note: This will use speech recognition and text-to-speech")
        print()
        
        # Test parameters
        test_email = "<EMAIL>"
        test_subject = "Voice Confirmation Test"
        test_body = "This is a test email to verify voice confirmation works correctly."
        
        print(f"📧 Email Details:")
        print(f"   To: {test_email}")
        print(f"   Subject: {test_subject}")
        print(f"   Body: {test_body}")
        print()
        
        print("🎤 Starting voice confirmation test...")
        print("📢 The system will ask for confirmation via speech.")
        print("🗣️  You should respond with 'yes' to confirm or 'no' to decline.")
        print()
        
        # Test the voice confirmation
        result = _get_voice_confirmation(test_email, test_subject, test_body)
        
        print()
        print("📊 VOICE CONFIRMATION RESULT:")
        if result:
            print("✅ User CONFIRMED - Email would be sent automatically")
        else:
            print("❌ User DECLINED or confirmation failed - Manual sending required")
        
        return result
        
    except Exception as e:
        print(f"❌ Error in voice confirmation test: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_complete_email_flow():
    """Test the complete email flow with voice confirmation"""
    print("\n" + "=" * 50)
    print("📧 COMPLETE EMAIL FLOW TEST")
    print("=" * 50)
    
    try:
        from Backend.Automation import SendEmail
        
        print("🚀 Testing complete email automation flow...")
        print("📝 This includes: compose → voice confirmation → auto-send")
        print()
        
        # Test with a safe email address
        test_email = "<EMAIL>"
        test_subject = "Matrix AI Complete Flow Test"
        test_body = "This tests the complete email automation flow with voice confirmation."
        
        print(f"📧 Email Details:")
        print(f"   To: {test_email}")
        print(f"   Subject: {test_subject}")
        print(f"   Body: {test_body}")
        print()
        
        print("🎯 Starting complete email flow...")
        print("📋 Expected sequence:")
        print("   1. Gmail compose window opens")
        print("   2. System asks for voice confirmation")
        print("   3. You respond with 'yes' or 'no'")
        print("   4. If 'yes': automatic sending attempts")
        print("   5. If 'no': manual sending required")
        print()
        
        # Call the complete SendEmail function
        success, message = SendEmail(test_email, test_subject, test_body)
        
        print()
        print("📊 COMPLETE FLOW RESULT:")
        print(f"   Success: {success}")
        print(f"   Message: {message}")
        
        if success:
            if "sent successfully" in message.lower():
                print("🎉 EMAIL SENT AUTOMATICALLY!")
            else:
                print("📋 Compose window opened - manual action required")
        else:
            print("❌ Email flow failed")
        
        return success
        
    except Exception as e:
        print(f"❌ Error in complete email flow test: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function"""
    print("🤖 Matrix AI Email Voice Confirmation Tests")
    print("=" * 60)
    print()
    
    print("📋 Test Overview:")
    print("   Test 1: Voice confirmation system only")
    print("   Test 2: Complete email flow with voice confirmation")
    print()
    
    # Check if automation is enabled
    try:
        from dotenv import dotenv_values
        env_values = dotenv_values(".env")
        auto_send = env_values.get("AUTO_SEND_EMAILS", "False").lower() == "true"
        
        print(f"⚙️  AUTO_SEND_EMAILS setting: {auto_send}")
        if not auto_send:
            print("⚠️  Voice confirmation will not be tested since AUTO_SEND_EMAILS=False")
            print("💡 Set AUTO_SEND_EMAILS=True in .env file to test voice confirmation")
            return
    except Exception as e:
        print(f"⚠️  Could not check AUTO_SEND_EMAILS setting: {e}")
    
    print()
    
    # Test 1: Voice confirmation only
    print("🧪 TEST 1: Voice Confirmation System")
    print("-" * 40)
    voice_test_result = test_voice_confirmation()
    
    # Test 2: Complete email flow
    complete_test_result = test_complete_email_flow()
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)
    print(f"Voice Confirmation Test: {'✅ PASSED' if voice_test_result else '❌ FAILED'}")
    print(f"Complete Email Flow Test: {'✅ PASSED' if complete_test_result else '❌ FAILED'}")
    
    if voice_test_result and complete_test_result:
        print("\n🎉 ALL TESTS PASSED!")
        print("✅ Voice confirmation email automation is working correctly")
        print()
        print("📋 Usage Instructions:")
        print("1. Say: 'send mail to [email address]'")
        print("2. Provide subject when asked")
        print("3. Provide email content when asked")
        print("4. Review the Gmail compose window that opens")
        print("5. Say 'yes' when asked for confirmation to auto-send")
        print("6. Say 'no' to keep it open for manual sending")
    else:
        print("\n⚠️ SOME TESTS FAILED")
        print("🔧 Please check the error messages above")
    
    print("\n🎯 Test completed!")

if __name__ == "__main__":
    main()
