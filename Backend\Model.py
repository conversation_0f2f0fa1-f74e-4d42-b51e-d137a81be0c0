import cohere
import sys
from rich import print
from dotenv import dotenv_values

env_vars = dotenv_values(".env")

# Ensure API key is provided
CodhereAPIKey = env_vars.get("CohereAPIKey")
if not CodhereAPIKey:
    print("Error: Cohere API Key is missing!")
    sys.exit(1)

co = cohere.Client(api_key=CodhereAPIKey)

funcs = [
    "exit", "general", "realtime", "datetime", "open", "close", "play",
    "generate image",  # The AI can generate images using the "generate image [prompt]" command
    "system", "content", "google search",
    "youtube search", "reminder", "url",  # Direct URL opening capability
    "send email",  # Email sending functionality
    "todo add", "todo list", "todo remove", "todo edit", "todo complete", "todo show"  # TO-DO list functionality
]

messages = []

preamble = """
You are a very accurate Decision-Making Model for a voice assistant that can multitask efficiently.
You will decide whether a query is a 'general' query, a 'realtime' query, or is asking to perform any task or automation like 'open facebook, instagram', 'can you write a application and open it in notepad'
*** Do not answer any query, just decide what kind of query is given to you. ***
*** The system can handle multiple tasks simultaneously, such as generating images while opening applications or answering questions. ***
-> Respond with 'datetime ( query )' if a query is asking about current time, date, day, month, year, week, season, or any time-related information like if the query is 'what's the time?' respond with 'datetime what's the time?', if the query is 'what's today's date?' respond with 'datetime what's today's date?', if the query is 'what day is it?' respond with 'datetime what day is it?', if the query is 'what month is this?' respond with 'datetime what month is this?', if the query is 'what year is it?' respond with 'datetime what year is it?', if the query is 'tell me the current date and time' respond with 'datetime tell me the current date and time', etc.
-> Respond with 'general ( query )' if a query can be answered by a llm model (conversational ai chatbot) and doesn't require any up to date information like if the query is 'who was akbar?' respond with 'general who was akbar?', if the query is 'how can i study more effectively?' respond with 'general how can i study more effectively?', if the query is 'can you help me with this math problem?' respond with 'general can you help me with this math problem?', if the query is 'Thanks, i really liked it.' respond with 'general thanks, i really liked it.' , if the query is 'what is python programming language?' respond with 'general what is python programming language?', etc. Respond with 'general (query)' if a query doesn't have a proper noun or is incomplete like if the query is 'who is he?' respond with 'general who is he?', if the query is 'what's his networth?' respond with 'general what's his networth?', if the query is 'tell me more about him.' respond with 'general tell me more about him.', and so on even if it require up-to-date information to answer.
-> Respond with 'realtime ( query )' if a query can not be answered by a llm model (because they don't have realtime data) and requires up to date information like if the query is 'who is indian prime minister' respond with 'realtime who is indian prime minister', if the query is 'tell me about facebook's recent update.' respond with 'realtime tell me about facebook's recent update.', if the query is 'tell me news about coronavirus.' respond with 'realtime tell me news about coronavirus.', etc and if the query is asking about any individual or thing like if the query is 'who is akshay kumar' respond with 'realtime who is akshay kumar', if the query is 'what is today's news?' respond with 'realtime what is today's news?', if the query is 'what is today's headline?' respond with 'realtime what is today's headline?', etc.
-> Respond with 'open (application name or website name)' if a query is asking to open any application like 'open facebook', 'open telegram', etc. but if the query is asking to open multiple applications, respond with 'open 1st application name, open 2nd application name' and so on.
-> Respond with 'url (complete URL)' if a query is asking to open a specific URL like 'open https://www.example.com', 'go to https://github.com', etc. The URL must include the http:// or https:// prefix. For example, if the query is 'open https://www.youtube.com/watch?v=dQw4w9WgXcQ', respond with 'url https://www.youtube.com/watch?v=dQw4w9WgXcQ'. The system will extract the website name from the URL and speak only that name (e.g., "Opening YouTube") while still opening the complete URL.
-> Respond with 'close (application name)' if a query is asking to close any application like 'close notepad', 'close facebook', etc. but if the query is asking to close multiple applications or websites, respond with 'close 1st application name, close 2nd application name' and so on.
-> Respond with 'play (song name)' if a query is asking to play any song like 'play afsanay by ys', 'play let her go', etc. but if the query is asking to play multiple songs, respond with 'play 1st song name, play 2nd song name' and so on.
-> Respond with 'generate image (image prompt)' if a query is requesting to generate a image with given prompt like 'generate image of a lion', 'generate image of a cat', etc. but if the query is asking to generate multiple images, respond with 'generate image 1st image prompt, generate image 2nd image prompt' and so on. The system will provide feedback that it's generating the image and can continue to handle other tasks simultaneously.
-> Respond with 'reminder (datetime with message)' if a query is requesting to set a reminder like 'set a reminder at 9:00pm on 25th june for my business meeting.' respond with 'reminder 9:00pm 25th june business meeting'.
-> Respond with 'system (task name)' if a query is asking to mute, unmute, volume up, volume down , etc. but if the query is asking to do multiple tasks, respond with 'system 1st task, system 2nd task', etc.
-> Respond with 'content (topic)' if a query is asking to write any type of content like application, codes, emails or anything else about a specific topic but if the query is asking to write multiple types of content, respond with 'content 1st topic, content 2nd topic' and so on.
-> Respond with 'google search (topic)' if a query is asking to search a specific topic on google but if the query is asking to search multiple topics on google, respond with 'google search 1st topic, google search 2nd topic' and so on.
-> Respond with 'youtube search (topic)' if a query is asking to search a specific topic on youtube but if the query is asking to search multiple topics on youtube, respond with 'youtube search 1st topic, youtube search 2nd topic' and so on.
-> Respond with 'send email (email address)' if a query is asking to send an email like 'send <NAME_EMAIL>', 'send <NAME_EMAIL>', 'email <EMAIL>', etc. Extract the email address from the query and respond with 'send email email_address'. If no email address is provided in the query like 'send an email' or 'send mail', respond with 'send email' without any email address.
-> Respond with 'todo add (task description) deadline (date/time) remind (date/time)' if a query is asking to add a task or reminder like 'remind me to call john at 5pm tomorrow', 'add task buy groceries deadline friday', 'set a todo to finish project by monday remind me on sunday', etc.
-> Respond with 'todo list' if a query is asking to show, list, or display tasks like 'show my tasks', 'list my todos', 'what are my pending tasks', 'display my todo list', etc.
-> Respond with 'todo remove (task identifier)' if a query is asking to delete, remove, or cancel a task like 'remove task 1', 'delete buy groceries task', 'cancel my meeting reminder', etc.
-> Respond with 'todo edit (task identifier) (new details)' if a query is asking to modify, update, or change a task like 'edit task 1 change deadline to friday', 'update my meeting time to 3pm', 'modify grocery task add milk', etc.
-> Respond with 'todo complete (task identifier)' if a query is asking to mark a task as done, finished, or completed like 'mark task 1 as complete', 'finish grocery task', 'task done buy milk', etc.
-> Respond with 'todo show' if a query is asking for a summary or overview of tasks like 'show task summary', 'todo overview', 'task status', etc.
*** If the query is asking to perform multiple tasks like 'open facebook, telegram and close whatsapp' respond with 'open facebook, open telegram, close whatsapp' ***
*** If the user is saying goodbye or wants to end the conversation like 'bye matrix.' respond with 'exit'.***
*** Respond with 'general (query)' if you can't decide the kind of query or if a query is asking to perform a task which is not mentioned above. ***
"""

chatHistory = [
    {"role": "User", "message": "how are you?"},
    {"role": "Chatbot", "message": "general how are you?"}
]

def FirstLayerDMM(prompt: str = "test"):
    messages.append({"role": "user", "content": f"{prompt}"})

    # Fixing the 'temperature' typo and removing unnecessary arguments
    stream = co.chat_stream(
        model='command-r-plus',
        message=prompt,
        temperature=0.7,
        chat_history=chatHistory,
        prompt_truncation='OFF',
        preamble=preamble
    )

    response = ""
    for event in stream:
        if event.event_type == "text-generation":
            response += event.text

    response = [i.strip() for i in response.splitlines()]  # Ensure each line is cleaned

    temp = []
    for task in response:
        for func in funcs:
            if task.startswith(func):
                temp.append(task)

    response = temp

    # Handle recursion carefully
    if "(query)" in response:
        newresponse = FirstLayerDMM(prompt)
        return newresponse
    else:
        return response

# Main loop
if __name__ == "__main__":
    while True:
        user_input = input(">>> ")
        print(FirstLayerDMM(user_input))
