import os
from dotenv import dotenv_values

# Load environment variables
env_vars = dotenv_values(".env")

# Get current working directory
current_dir = os.getcwd()

# Define directory paths
TempDirectoryPath = os.path.join(current_dir, "Frontend", "Files")
GraphicsDirectoryPath = os.path.join(current_dir, "Graphics")

# Ensure directories exist
os.makedirs(TempDirectoryPath, exist_ok=True)
os.makedirs(GraphicsDirectoryPath, exist_ok=True)

# Environment variables
Username = env_vars.get("Username", "User")
Assistantname = env_vars.get("Assistantname", "Matrix")

# Print paths for debugging
print(f"TempDirectoryPath: {TempDirectoryPath}")
print(f"GraphicsDirectoryPath: {GraphicsDirectoryPath}")
