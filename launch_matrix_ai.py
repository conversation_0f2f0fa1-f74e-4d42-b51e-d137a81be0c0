#!/usr/bin/env python3
"""
Matrix AI Launcher - DEPRECATED
This launcher is deprecated and may cause duplicate instances.

RECOMMENDED LAUNCH METHODS:
1. python main.py
2. Start_Matrix_AI.bat

This file now redirects to the correct launch method.
"""

import subprocess
import sys

def main():
    """Redirect to the correct launch method"""
    print("=" * 60)
    print("⚠️  DEPRECATED LAUNCHER")
    print("=" * 60)
    print("This launcher (launch_matrix_ai.py) is deprecated.")
    print("It may cause duplicate instances and conflicts.")
    print("")
    print("RECOMMENDED LAUNCH METHODS:")
    print("1. python main.py")
    print("2. Start_Matrix_AI.bat")
    print("")
    print("Redirecting to the correct method...")
    print("=" * 60)

    try:
        # Launch the correct way - just main.py (which handles the animation internally)
        print("🚀 Launching Matrix AI via main.py...")
        subprocess.run([sys.executable, "main.py"], check=True)
    except subprocess.CalledProcessError as e:
        print(f"❌ Application ended: {e}")
    except Exception as e:
        print(f"❌ Error launching application: {e}")

    print("🎯 Matrix AI Session Complete!")

if __name__ == "__main__":
    main()
