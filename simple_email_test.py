#!/usr/bin/env python3
"""
Simple test for email automation using keyboard shortcuts.
This is a more reliable approach than trying to find the Send button.
"""

import time
import urllib.parse

def simple_gmail_automation(email_address, subject="Test Subject", body="Test Body"):
    """
    Simple Gmail automation using keyboard shortcuts.
    """
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.service import Service
        from selenium.webdriver.chrome.options import Options
        from selenium.webdriver.common.keys import Keys
        from selenium.webdriver.common.action_chains import ActionChains
        from selenium.webdriver.support.ui import WebDriverWait
        from webdriver_manager.chrome import ChromeDriverManager
        
        print("🚀 Starting simple Gmail automation...")
        
        # Setup Chrome options
        chrome_options = Options()
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--window-size=1200,800")
        
        # Create WebDriver
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=chrome_options)
        
        try:
            # Create Gmail compose URL
            encoded_email = urllib.parse.quote(email_address)
            encoded_subject = urllib.parse.quote(subject)
            encoded_body = urllib.parse.quote(body)
            
            gmail_url = f"https://mail.google.com/mail/?view=cm&fs=1&to={encoded_email}&su={encoded_subject}&body={encoded_body}"
            
            print(f"📧 Opening Gmail for: {email_address}")
            driver.get(gmail_url)
            
            # Wait for page to load
            time.sleep(10)
            
            # Check if we're signed in
            if "accounts.google.com" in driver.current_url:
                print("❌ Not signed into Gmail. Please sign in first.")
                return False
            
            print("✅ Gmail loaded successfully")
            print("⌨️  Attempting to send with Ctrl+Enter...")
            
            # Wait a bit more for compose window to fully load
            time.sleep(5)
            
            # Use keyboard shortcut to send (most reliable method)
            ActionChains(driver).send_keys(Keys.CONTROL + Keys.ENTER).perform()
            
            print("📤 Send command executed!")
            time.sleep(3)
            
            # Check if we're back to inbox (indicates email was sent)
            current_url = driver.current_url
            if "compose" not in current_url.lower():
                print("✅ Email appears to have been sent successfully!")
                return True
            else:
                print("⚠️ Still in compose window - email may not have been sent")
                return False
                
        finally:
            # Always close the browser
            time.sleep(2)
            driver.quit()
            print("🔒 Browser closed")
            
    except Exception as e:
        print(f"❌ Error in Gmail automation: {e}")
        return False

def test_simple_automation():
    """Test the simple automation"""
    print("=" * 50)
    print("🧪 SIMPLE GMAIL AUTOMATION TEST")
    print("=" * 50)
    
    # Test with a safe email (change this to your email for testing)
    test_email = "<EMAIL>"
    test_subject = "Matrix AI Test - Simple Automation"
    test_body = "This is a test email from the simplified Matrix AI automation."
    
    print(f"📧 Testing email to: {test_email}")
    print(f"📝 Subject: {test_subject}")
    print(f"💬 Body: {test_body}")
    print()
    
    success = simple_gmail_automation(test_email, test_subject, test_body)
    
    print()
    print("=" * 50)
    if success:
        print("🎉 AUTOMATION TEST SUCCESSFUL!")
        print("✅ Email should have been sent automatically")
    else:
        print("❌ AUTOMATION TEST FAILED")
        print("⚠️ Check the error messages above")
    print("=" * 50)
    
    return success

if __name__ == "__main__":
    test_simple_automation()
