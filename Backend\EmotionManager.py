import os
import json
import time
from typing import Dict, List, Optional
from collections import deque

class EmotionManager:
    """
    Manages emotion data storage, retrieval, and context for the AI assistant.
    Provides easy access to emotion information for other modules.
    """
    
    def __init__(self):
        self.data_dir = os.path.join(os.getcwd(), "Data")
        self.temp_dir = os.path.join(os.getcwd(), "Frontend", "Files")
        os.makedirs(self.data_dir, exist_ok=True)
        os.makedirs(self.temp_dir, exist_ok=True)
        
        self.emotion_file = os.path.join(self.data_dir, "EmotionData.json")
        self.context_file = os.path.join(self.temp_dir, "EmotionContext.json")
        self.history_file = os.path.join(self.data_dir, "EmotionHistory.json")
        
        # Initialize emotion history
        self.emotion_history = deque(maxlen=50)
        self.load_emotion_history()
    
    def get_current_emotion(self) -> Dict[str, any]:
        """Get the current emotion state."""
        try:
            if os.path.exists(self.emotion_file):
                with open(self.emotion_file, "r", encoding="utf-8") as f:
                    data = json.load(f)
                    return {
                        "emotion": data.get("current_emotion", "neutral"),
                        "confidence": data.get("confidence", 0.0),
                        "timestamp": data.get("last_updated", time.time())
                    }
        except Exception as e:
            print(f"Error reading emotion data: {e}")
        
        return {"emotion": "neutral", "confidence": 0.0, "timestamp": time.time()}
    
    def get_emotion_context(self) -> Dict[str, any]:
        """Get the latest speech-emotion context."""
        try:
            if os.path.exists(self.context_file):
                with open(self.context_file, "r", encoding="utf-8") as f:
                    return json.load(f)
        except Exception as e:
            print(f"Error reading emotion context: {e}")
        
        return {"text": "", "emotion": "neutral", "confidence": 0.0, "timestamp": time.time()}
    
    def get_emotion_history(self, count: int = 10) -> List[Dict]:
        """Get recent emotion history."""
        return list(self.emotion_history)[-count:]
    
    def add_emotion_record(self, emotion: str, confidence: float, context: str = ""):
        """Add a new emotion record to history."""
        record = {
            "emotion": emotion,
            "confidence": confidence,
            "context": context,
            "timestamp": time.time()
        }
        
        self.emotion_history.append(record)
        self.save_emotion_history()
    
    def get_dominant_emotion(self, time_window: int = 300) -> Dict[str, any]:
        """
        Get the dominant emotion over a time window (default: 5 minutes).
        
        Args:
            time_window: Time window in seconds
            
        Returns:
            Dictionary with dominant emotion info
        """
        current_time = time.time()
        recent_emotions = []
        
        # Get emotions within time window
        for record in reversed(list(self.emotion_history)):
            if current_time - record["timestamp"] <= time_window:
                recent_emotions.append(record)
            else:
                break
        
        if not recent_emotions:
            return {"emotion": "neutral", "confidence": 0.0, "count": 0}
        
        # Count emotions weighted by confidence
        emotion_weights = {}
        for record in recent_emotions:
            emotion = record["emotion"]
            confidence = record["confidence"]
            if emotion in emotion_weights:
                emotion_weights[emotion] += confidence
            else:
                emotion_weights[emotion] = confidence
        
        # Find dominant emotion
        dominant_emotion = max(emotion_weights, key=emotion_weights.get)
        total_weight = sum(emotion_weights.values())
        avg_confidence = emotion_weights[dominant_emotion] / len([r for r in recent_emotions if r["emotion"] == dominant_emotion])
        
        return {
            "emotion": dominant_emotion,
            "confidence": avg_confidence,
            "weight": emotion_weights[dominant_emotion],
            "total_weight": total_weight,
            "count": len(recent_emotions),
            "time_window": time_window
        }
    
    def get_emotion_trend(self, count: int = 5) -> str:
        """
        Analyze emotion trend over recent records.
        
        Args:
            count: Number of recent records to analyze
            
        Returns:
            Trend description (improving, declining, stable, mixed)
        """
        if len(self.emotion_history) < count:
            return "insufficient_data"
        
        recent = list(self.emotion_history)[-count:]
        
        # Define emotion valence (positive/negative scale)
        emotion_valence = {
            "happy": 1.0,
            "excited": 0.8,
            "calm": 0.6,
            "neutral": 0.0,
            "sad": -0.6,
            "angry": -0.8
        }
        
        # Calculate valence trend
        valences = []
        for record in recent:
            emotion = record["emotion"]
            confidence = record["confidence"]
            valence = emotion_valence.get(emotion, 0.0) * confidence
            valences.append(valence)
        
        if len(valences) < 2:
            return "stable"
        
        # Calculate trend
        first_half = sum(valences[:len(valences)//2]) / (len(valences)//2)
        second_half = sum(valences[len(valences)//2:]) / (len(valences) - len(valences)//2)
        
        difference = second_half - first_half
        
        if difference > 0.2:
            return "improving"
        elif difference < -0.2:
            return "declining"
        elif abs(difference) < 0.1:
            return "stable"
        else:
            return "mixed"
    
    def get_emotion_summary(self) -> Dict[str, any]:
        """Get a comprehensive emotion summary."""
        current = self.get_current_emotion()
        context = self.get_emotion_context()
        dominant = self.get_dominant_emotion()
        trend = self.get_emotion_trend()
        
        return {
            "current": current,
            "context": context,
            "dominant": dominant,
            "trend": trend,
            "history_count": len(self.emotion_history),
            "last_updated": time.time()
        }
    
    def save_emotion_history(self):
        """Save emotion history to file."""
        try:
            data = {
                "history": list(self.emotion_history),
                "last_updated": time.time()
            }
            with open(self.history_file, "w", encoding="utf-8") as f:
                json.dump(data, f, indent=2)
        except Exception as e:
            print(f"Error saving emotion history: {e}")
    
    def load_emotion_history(self):
        """Load emotion history from file."""
        try:
            if os.path.exists(self.history_file):
                with open(self.history_file, "r", encoding="utf-8") as f:
                    data = json.load(f)
                    history = data.get("history", [])
                    # Load recent history (last 30 items)
                    self.emotion_history = deque(history[-30:], maxlen=50)
        except Exception as e:
            print(f"Error loading emotion history: {e}")
    
    def clear_emotion_data(self):
        """Clear all emotion data."""
        self.emotion_history.clear()
        
        # Remove files
        for file_path in [self.emotion_file, self.context_file, self.history_file]:
            if os.path.exists(file_path):
                try:
                    os.remove(file_path)
                except Exception as e:
                    print(f"Error removing {file_path}: {e}")
    
    def export_emotion_data(self, file_path: str):
        """Export emotion data to a file."""
        try:
            export_data = {
                "current": self.get_current_emotion(),
                "context": self.get_emotion_context(),
                "history": list(self.emotion_history),
                "summary": self.get_emotion_summary(),
                "exported_at": time.time()
            }
            
            with open(file_path, "w", encoding="utf-8") as f:
                json.dump(export_data, f, indent=2)
                
            print(f"Emotion data exported to {file_path}")
        except Exception as e:
            print(f"Error exporting emotion data: {e}")

# Global emotion manager instance
emotion_manager = EmotionManager()

# Convenience functions for easy access
def get_current_emotion() -> Dict[str, any]:
    """Get current emotion state."""
    return emotion_manager.get_current_emotion()

def get_emotion_context() -> Dict[str, any]:
    """Get latest speech-emotion context."""
    return emotion_manager.get_emotion_context()

def get_emotion_summary() -> Dict[str, any]:
    """Get comprehensive emotion summary."""
    return emotion_manager.get_emotion_summary()

def get_dominant_emotion(time_window: int = 300) -> Dict[str, any]:
    """Get dominant emotion over time window."""
    return emotion_manager.get_dominant_emotion(time_window)

def add_emotion_record(emotion: str, confidence: float, context: str = ""):
    """Add emotion record to history."""
    emotion_manager.add_emotion_record(emotion, confidence, context)

if __name__ == "__main__":
    # Test the emotion manager
    print("Testing Emotion Manager...")
    
    # Add some test records
    test_emotions = [
        ("happy", 0.8, "User greeting"),
        ("neutral", 0.6, "Normal conversation"),
        ("excited", 0.9, "Positive response"),
        ("calm", 0.7, "Relaxed interaction")
    ]
    
    for emotion, confidence, context in test_emotions:
        add_emotion_record(emotion, confidence, context)
        time.sleep(0.1)  # Small delay to create different timestamps
    
    # Test functions
    print(f"Current emotion: {get_current_emotion()}")
    print(f"Dominant emotion: {get_dominant_emotion()}")
    print(f"Emotion summary: {get_emotion_summary()}")
