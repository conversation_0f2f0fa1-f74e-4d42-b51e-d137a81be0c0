import os
import json
import queue
import threading
import time
import sounddevice as sd
import numpy as np
from vosk import Model, KaldiRecognizer, SetLogLevel
import mtranslate as mt
from dotenv import dotenv_values
import requests
import zipfile
import io
import tqdm
from collections import deque

# Import emotion analysis
try:
    from Backend.EmotionAnalysis import (
        analyze_voice_emotion,
        get_current_emotion,
        emotion_analyzer,
        set_emotion_performance_mode
    )
    EMOTION_ANALYSIS_AVAILABLE = True
    print("Emotion analysis module loaded successfully")
    # Set to fast mode for real-time processing
    set_emotion_performance_mode("fast")
    print("Emotion analysis set to fast mode for optimal performance")
except ImportError as e:
    print(f"Emotion analysis not available: {e}")
    EMOTION_ANALYSIS_AVAILABLE = False

# Set Vosk logging level to reduce noise (0 for silent, higher for more verbose)
SetLogLevel(-1)

# Load environment variables
env_vars = dotenv_values(".env")
InputLanguage = env_vars.get("InputLanguage", "en-US")

# Define paths
current_dir = os.getcwd()
TempDirPath = os.path.join(current_dir, "Frontend", "Files")
os.makedirs(TempDirPath, exist_ok=True)

# Model paths
MODELS_DIR = os.path.join(current_dir, "Data", "VoskModels")
os.makedirs(MODELS_DIR, exist_ok=True)

# Map language codes to Vosk model names - using better models when available
LANGUAGE_TO_MODEL = {
    "en": "vosk-model-small-en-us-0.15",  # Default small model
    "hi": "vosk-model-small-hi-0.22",
    # Add more languages as needed
}

# Fallback to smaller models if larger ones fail to download
FALLBACK_MODELS = {
    "en": "vosk-model-small-en-us-0.15",
    "hi": "vosk-model-small-hi-0.22",
}

# Audio parameters - OPTIMIZED FOR SPEED
SAMPLE_RATE = 16000
CHANNELS = 1
CHUNK_SIZE = 2000  # Reduced from 4000 to 2000 for faster processing (0.125 seconds)

# Speech detection parameters - OPTIMIZED FOR SPEED
ENERGY_THRESHOLD = 250  # Reduced from 300 to 250 for more sensitive detection
DYNAMIC_ENERGY_ADJUSTMENT = True  # Dynamically adjust energy threshold
DYNAMIC_ENERGY_RATIO = 1.5  # Ratio for dynamic energy adjustment
SPEECH_START_TIMEOUT = 2.0  # Reduced from 3.0 to 2.0 seconds for faster start
SPEECH_END_TIMEOUT = 0.7  # Reduced from 1.0 to 0.7 seconds for faster end detection

# Global variables
recognizer = None
audio_queue = queue.Queue()
stop_recording = threading.Event()
is_listening = False
model_loaded = False
energy_threshold = ENERGY_THRESHOLD
recent_audio = deque(maxlen=10)  # Store recent audio for better context

def download_vosk_model(model_name, output_dir=MODELS_DIR):
    """
    Download a Vosk model from the official repository.

    Args:
        model_name: Name of the model to download
        output_dir: Directory to save the model
    """
    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)

    # Check if model already exists
    model_dir = os.path.join(output_dir, model_name)
    if os.path.exists(model_dir):
        print(f"Model {model_name} already exists at {model_dir}")
        return model_dir

    # Download the model
    url = f"https://alphacephei.com/vosk/models/{model_name}.zip"
    print(f"Downloading model from {url}...")

    try:
        response = requests.get(url, stream=True)
        response.raise_for_status()

        # Get total file size
        total_size = int(response.headers.get('content-length', 0))

        # Download with progress bar
        with tqdm.tqdm(total=total_size, unit='B', unit_scale=True, desc=model_name) as pbar:
            content = io.BytesIO()
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    content.write(chunk)
                    pbar.update(len(chunk))

        # Extract the model
        print(f"Extracting model to {output_dir}...")
        content.seek(0)
        with zipfile.ZipFile(content) as z:
            z.extractall(output_dir)

        print(f"Model downloaded and extracted to {model_dir}")
        return model_dir
    except Exception as e:
        print(f"Error downloading model: {e}")
        return None

def get_model_for_language(language_code):
    """Get the appropriate model for the given language code."""
    # Extract the primary language code (e.g., 'en-US' -> 'en')
    primary_code = language_code.split('-')[0].lower()

    # Try to get a better model first (if available)
    better_models = {
        "en": "vosk-model-en-us-0.22",  # Larger English model for better accuracy
    }

    # First try to use a better model if available
    if primary_code in better_models:
        better_model_name = better_models[primary_code]
        better_model_path = os.path.join(MODELS_DIR, better_model_name)

        # If better model already exists, use it
        if os.path.exists(better_model_path):
            print(f"Using better model: {better_model_name}")
            return better_model_path

        # Try to download the better model
        print(f"Trying to download better model: {better_model_name}")
        better_model_path = download_vosk_model(better_model_name)
        if better_model_path:
            print(f"Successfully downloaded better model: {better_model_name}")
            return better_model_path
        print(f"Failed to download better model, falling back to smaller model")

    # Get the standard model name for this language
    model_name = LANGUAGE_TO_MODEL.get(primary_code, LANGUAGE_TO_MODEL["en"])

    # Check if the model exists, download if not
    model_path = os.path.join(MODELS_DIR, model_name)
    if not os.path.exists(model_path):
        print(f"Model for language {language_code} not found. Downloading...")
        model_path = download_vosk_model(model_name)
        if not model_path:
            print(f"Failed to download model for {language_code}. Using English model.")
            model_path = download_vosk_model(LANGUAGE_TO_MODEL["en"])

    return model_path

def initialize_recognizer():
    """Initialize the speech recognizer with the appropriate model."""
    global recognizer, model_loaded

    try:
        # Get the model path for the current language using the improved function
        model_path = get_model_for_language(InputLanguage)

        # If we couldn't get a model, we can't proceed
        if not model_path or not os.path.exists(model_path):
            print("No models found. Speech recognition will not work.")
            model_loaded = False
            return False

        print(f"Loading Vosk model from {model_path}...")
        model = Model(model_path)
        recognizer = KaldiRecognizer(model, SAMPLE_RATE)
        recognizer.SetWords(True)

        # Set additional parameters for better recognition
        recognizer.SetMaxAlternatives(5)  # Get multiple recognition alternatives

        model_loaded = True
        print("Vosk model loaded successfully")
        return True
    except Exception as e:
        print(f"Error initializing recognizer: {e}")
        model_loaded = False
        return False

def calculate_energy(audio_data):
    """Calculate the energy level of an audio chunk."""
    # Convert to int16 if needed
    if isinstance(audio_data, bytes):
        audio_data = np.frombuffer(audio_data, dtype=np.int16)

    # Calculate RMS energy
    return np.sqrt(np.mean(np.square(audio_data.astype(np.float32))))

def is_speech(audio_data, threshold=None):
    """Determine if audio chunk contains speech based on energy level."""
    if threshold is None:
        threshold = energy_threshold

    energy = calculate_energy(audio_data)
    return energy > threshold

def audio_callback(indata, frames=None, time_info=None, status=None):
    """Callback for sounddevice to capture audio data with noise filtering."""
    global energy_threshold

    if status:
        print(f"Audio callback status: {status}")

    if not stop_recording.is_set():
        # Convert to bytes for processing
        audio_bytes = bytes(indata)

        # Store in recent audio buffer
        recent_audio.append(audio_bytes)

        # Perform emotion analysis on audio chunk if available
        if EMOTION_ANALYSIS_AVAILABLE and len(indata) > 0:
            try:
                # Analyze emotion from current audio chunk
                emotion, confidence = analyze_voice_emotion(indata, SAMPLE_RATE)
                # Only print if confidence is reasonably high to avoid spam
                if confidence > 0.6:
                    print(f"Detected emotion: {emotion} (confidence: {confidence:.2f})")
            except Exception as e:
                # Silently handle emotion analysis errors to not disrupt speech recognition
                pass

        # Dynamically adjust energy threshold if enabled
        if DYNAMIC_ENERGY_ADJUSTMENT:
            energy = calculate_energy(indata)
            # If current energy is significantly higher than threshold, adjust threshold
            if energy > energy_threshold * 3:
                energy_threshold = min(energy_threshold * DYNAMIC_ENERGY_RATIO, 4000)
            # Gradually decrease threshold if consistently low energy
            elif len(recent_audio) >= 5 and all(calculate_energy(chunk) < energy_threshold for chunk in list(recent_audio)[-5:]):
                energy_threshold = max(ENERGY_THRESHOLD, energy_threshold / DYNAMIC_ENERGY_RATIO)

        # Put audio in queue for processing
        audio_queue.put(audio_bytes)

def SetAssistantStatus(Status):
    """Update the assistant's status."""
    with open(os.path.join(TempDirPath, "Status.data"), "w", encoding="utf-8") as file:
        file.write(Status)

def QueryModifier(Query):
    """Format the query with proper punctuation and capitalization."""
    if not Query or Query.strip() == "":
        return "I didn't catch that."

    new_query = Query.lower().strip()
    query_words = new_query.split()

    if not query_words:
        return "I didn't catch that."

    question_words = ["how", "what", "who", "where", "when", "why", "which", "whose", "whom", "can you", "where's", "what's", "how's"]

    if any(word + " " in new_query for word in question_words):
        if len(query_words) > 0 and query_words[-1][-1] in ['.', '?', '!']:
            new_query = new_query[:-1] + "?"
        else:
            new_query += "?"
    else:
        if len(query_words) > 0 and query_words[-1][-1] in ['.', '?', '!']:
            new_query = new_query[:-1] + "."
        else:
            new_query += "."

    return new_query.capitalize()

def UniversalTranslator(Text):
    """Translate text to English from any detected language."""
    try:
        english_translation = mt.translate(Text, "en", "auto")
        return english_translation.capitalize()
    except Exception as e:
        print(f"Translation error: {e}")
        return Text.capitalize()  # Return original text if translation fails

def get_emotion_context():
    """Get current emotion context for speech processing."""
    if EMOTION_ANALYSIS_AVAILABLE:
        try:
            emotion_data = get_current_emotion()
            return emotion_data
        except Exception as e:
            print(f"Error getting emotion context: {e}")
    return {"emotion": "neutral", "confidence": 0.0, "timestamp": time.time()}

def save_emotion_context(text_result):
    """Save the speech recognition result with emotion context."""
    if EMOTION_ANALYSIS_AVAILABLE:
        try:
            emotion_data = get_current_emotion()
            context_data = {
                "text": text_result,
                "emotion": emotion_data.get("emotion", "neutral"),
                "confidence": emotion_data.get("confidence", 0.0),
                "timestamp": time.time()
            }

            # Save to emotion context file
            context_file = os.path.join(TempDirPath, "EmotionContext.json")
            with open(context_file, "w", encoding="utf-8") as f:
                json.dump(context_data, f, indent=2)

            print(f"Speech with emotion context saved: {emotion_data.get('emotion', 'neutral')} ({emotion_data.get('confidence', 0.0):.2f})")
        except Exception as e:
            print(f"Error saving emotion context: {e}")

def start_listening():
    """Start listening for speech."""
    global is_listening, stop_recording

    if is_listening:
        return

    if not model_loaded and not initialize_recognizer():
        return "Error initializing speech recognition model."

    # Clear any previous data
    while not audio_queue.empty():
        audio_queue.get()

    stop_recording.clear()
    is_listening = True

    try:
        print("Starting audio stream...")
        stream = sd.RawInputStream(
            samplerate=SAMPLE_RATE,
            blocksize=CHUNK_SIZE,
            dtype='int16',
            channels=CHANNELS,
            callback=audio_callback
        )
        stream.start()
        return stream
    except Exception as e:
        print(f"Error starting audio stream: {e}")
        is_listening = False
        return None

def stop_listening(stream=None):
    """Stop listening for speech."""
    global is_listening

    if stream:
        try:
            stream.stop()
            stream.close()
        except Exception as e:
            print(f"Error stopping stream: {e}")

    stop_recording.set()
    is_listening = False

def enhance_audio(audio_data):
    """Apply audio enhancement techniques to improve recognition quality."""
    # Convert bytes to numpy array if needed
    if isinstance(audio_data, bytes):
        audio_data = np.frombuffer(audio_data, dtype=np.int16)

    # Apply a simple noise gate
    audio_data = audio_data.copy()  # Create a copy to avoid modifying the original
    noise_gate = energy_threshold / 10
    audio_data[np.abs(audio_data) < noise_gate] = 0

    # Normalize audio to use full dynamic range
    if np.max(np.abs(audio_data)) > 0:
        audio_data = audio_data * (32767 / np.max(np.abs(audio_data)) * 0.9)

    return audio_data.astype(np.int16).tobytes()

def SpeechRecognition():
    """Advanced speech recognition function with improved accuracy and noise handling."""
    global recognizer, energy_threshold

    if not model_loaded and not initialize_recognizer():
        return "Error initializing speech recognition model."

    # Reset energy threshold to default at the start of each recognition
    energy_threshold = ENERGY_THRESHOLD

    SetAssistantStatus("Listening...")
    print("Starting speech recognition...")

    # Start listening
    stream = start_listening()
    if not stream:
        return "Error starting speech recognition."

    # Process audio until speech is recognized or timeout
    result_text = ""
    all_audio = bytearray()
    speech_detected = False
    silence_start = time.time()
    max_listen_time = 20.0  # Maximum listening time in seconds
    start_time = time.time()
    waiting_for_speech = True

    # Clear the recent audio buffer
    recent_audio.clear()

    try:
        print("Waiting for speech...")
        while time.time() - start_time < max_listen_time:
            # Get audio data from queue
            try:
                data = audio_queue.get(block=True, timeout=0.5)
            except queue.Empty:
                continue

            # Check if this chunk contains speech
            current_is_speech = is_speech(data)

            # Add to all audio for final processing
            all_audio.extend(data)

            # State machine for speech detection
            if waiting_for_speech:
                if current_is_speech:
                    # Speech just started
                    waiting_for_speech = False
                    speech_detected = True
                    silence_start = time.time()
                    print("Speech detected, listening...")
                    SetAssistantStatus("Speech detected...")
                elif time.time() - start_time > SPEECH_START_TIMEOUT:
                    # If no speech detected within timeout, check if we should continue waiting
                    if time.time() - start_time < max_listen_time / 2:
                        # Still have time, continue waiting
                        continue
                    else:
                        # Give up waiting for speech
                        print("No speech detected within timeout")
                        break
            else:
                # Already detected speech, now looking for end of speech
                if current_is_speech:
                    # Reset silence timer as we're still hearing speech
                    silence_start = time.time()
                elif time.time() - silence_start > SPEECH_END_TIMEOUT:
                    # Silence long enough to consider speech ended
                    print("End of speech detected")
                    break

            # Process audio with Vosk
            enhanced_data = enhance_audio(data)
            if recognizer.AcceptWaveform(enhanced_data):
                result_json = recognizer.Result()
                result = json.loads(result_json)

                if result.get("text", "").strip():
                    result_text = result.get("text", "")
                    print(f"Recognized: {result_text}")
            else:
                partial_json = recognizer.PartialResult()
                partial = json.loads(partial_json)
                partial_text = partial.get("partial", "")

                if partial_text.strip():
                    print(f"Partial: {partial_text}")

    except Exception as e:
        print(f"Error during speech recognition: {e}")
    finally:
        # Stop listening
        stop_listening(stream)

    # If we have collected audio but no result from incremental processing,
    # try processing all audio at once for better context
    if speech_detected and not result_text and len(all_audio) > 0:
        try:
            print("Processing complete audio for better results...")
            # Reset recognizer for full processing
            initialize_recognizer()

            # Process all audio at once
            enhanced_full_audio = enhance_audio(bytes(all_audio))
            recognizer.AcceptWaveform(enhanced_full_audio)
            final_result = json.loads(recognizer.FinalResult())

            if final_result.get("text", "").strip():
                result_text = final_result.get("text", "")
                print(f"Final recognition result: {result_text}")
        except Exception as e:
            print(f"Error during final audio processing: {e}")

    # Process the result
    if not result_text:
        if speech_detected:
            return "I heard something but couldn't understand it. Could you please repeat?"
        else:
            return "I didn't hear anything. Please try again."

    # Apply language processing
    final_result = ""
    if "en" in InputLanguage.lower():
        final_result = QueryModifier(result_text)
    else:
        SetAssistantStatus("Translating.....")
        final_result = QueryModifier(UniversalTranslator(result_text))

    # Save emotion context with the speech result
    save_emotion_context(final_result)

    return final_result

if __name__ == "__main__":
    # Initialize the recognizer
    initialize_recognizer()

    # Test speech recognition - automatically start without waiting for Enter
    while True:
        print("Starting speech recognition automatically...")
        result = SpeechRecognition()
        print(f"Result: {result}")
        time.sleep(1)  # Short pause between recognition attempts
