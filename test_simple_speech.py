#!/usr/bin/env python3
"""
Simple interactive test for Matrix AI speech recognition and response.
This script provides an easy way to test the speech-to-response functionality.
"""

import sys
import os
import time

# Add the current directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_single_interaction():
    """Test a single speech interaction"""
    print("🎤 Single Speech Interaction Test")
    print("=" * 50)
    
    try:
        from main import MainExecution
        from Frontend.GUI import SetMicrophoneStatus, GetMicrophoneStatus, SetAssistantStatus
        
        # Check and set microphone status
        original_status = GetMicrophoneStatus()
        print(f"📊 Current microphone status: {original_status}")
        
        if original_status.lower() != "true":
            print("🔧 Activating microphone for test...")
            SetMicrophoneStatus("True")
        
        print("\n🎯 SPEECH TEST READY")
        print("📋 Examples of what you can say:")
        print("   • 'Hello Matrix AI'")
        print("   • 'What time is it?'")
        print("   • 'Tell me a joke'")
        print("   • 'What's the weather today?'")
        print("   • 'Tell me about artificial intelligence'")
        print()
        
        input("Press Enter when ready to speak...")
        
        print("🗣️ LISTENING... Please speak clearly now!")
        SetAssistantStatus("Test - Listening...")
        
        start_time = time.time()
        result = MainExecution()
        end_time = time.time()
        
        print(f"\n⏱️ Total interaction time: {end_time - start_time:.2f} seconds")
        
        # Restore original microphone status
        if original_status.lower() != "true":
            SetMicrophoneStatus(original_status)
            print(f"🔧 Microphone status restored to: {original_status}")
        
        if result:
            print("✅ Speech interaction completed successfully!")
            return True
        else:
            print("❌ Speech interaction failed")
            return False
        
    except Exception as e:
        print(f"❌ Error during speech interaction: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_continuous_mode():
    """Test continuous speech interactions"""
    print("\n🔄 Continuous Speech Mode Test")
    print("=" * 50)
    
    try:
        from Frontend.GUI import SetMicrophoneStatus, GetMicrophoneStatus
        
        # Ensure microphone is active
        original_status = GetMicrophoneStatus()
        if original_status.lower() != "true":
            SetMicrophoneStatus("True")
        
        print("🎯 CONTINUOUS MODE")
        print("📋 This simulates how the AI works in normal operation")
        print("🔄 You can speak multiple commands in sequence")
        print("⏹️ Type 'quit' to stop")
        print()
        
        interaction_count = 0
        successful_interactions = 0
        
        while True:
            try:
                user_input = input(f"\n🎤 Interaction {interaction_count + 1} - Press Enter to speak (or type 'quit' to exit): ").strip().lower()
                
                if user_input == 'quit':
                    break
                
                print("🗣️ LISTENING... Speak now!")
                
                from main import MainExecution
                start_time = time.time()
                result = MainExecution()
                end_time = time.time()
                
                interaction_count += 1
                
                if result:
                    successful_interactions += 1
                    print(f"✅ Interaction {interaction_count} successful ({end_time - start_time:.2f}s)")
                else:
                    print(f"❌ Interaction {interaction_count} failed ({end_time - start_time:.2f}s)")
                
            except KeyboardInterrupt:
                print("\n⏹️ Stopping continuous mode...")
                break
            except Exception as e:
                print(f"❌ Error in interaction {interaction_count + 1}: {e}")
                interaction_count += 1
        
        # Restore original microphone status
        if original_status.lower() != "true":
            SetMicrophoneStatus(original_status)
        
        if interaction_count > 0:
            success_rate = (successful_interactions / interaction_count) * 100
            print(f"\n📊 CONTINUOUS MODE RESULTS:")
            print(f"   Total interactions: {interaction_count}")
            print(f"   Successful: {successful_interactions}")
            print(f"   Success rate: {success_rate:.1f}%")
            
            return success_rate >= 70  # 70% success rate threshold
        else:
            print("No interactions completed")
            return False
        
    except Exception as e:
        print(f"❌ Error in continuous mode: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_speech_recognition_only():
    """Test just the speech recognition component"""
    print("\n🎤 Speech Recognition Only Test")
    print("=" * 50)
    
    try:
        from Backend.SpeechToText import SpeechRecognition
        from Frontend.GUI import SetMicrophoneStatus, GetMicrophoneStatus, SetAssistantStatus
        
        # Ensure microphone is active
        original_status = GetMicrophoneStatus()
        if original_status.lower() != "true":
            SetMicrophoneStatus("True")
        
        print("🎯 SPEECH RECOGNITION TEST")
        print("📋 This tests only the speech recognition component")
        print("🗣️ Say something clearly when prompted")
        print()
        
        input("Press Enter when ready to test speech recognition...")
        
        print("🎤 LISTENING... Please speak now!")
        SetAssistantStatus("Test - Speech Recognition...")
        
        start_time = time.time()
        result = SpeechRecognition()
        end_time = time.time()
        
        print(f"\n📊 SPEECH RECOGNITION RESULTS:")
        print(f"   Recognition time: {end_time - start_time:.2f} seconds")
        print(f"   Recognized text: '{result}'")
        
        # Restore original microphone status
        if original_status.lower() != "true":
            SetMicrophoneStatus(original_status)
        
        # Check if recognition was successful
        if result and result not in [
            "I didn't hear anything. Please try again.",
            "I'm having trouble with speech recognition. Please try again later.",
            "I didn't catch that.",
            "Error in speech recognition"
        ]:
            print("✅ Speech recognition working correctly!")
            return True, result
        else:
            print("❌ Speech recognition failed or no input detected")
            return False, result
        
    except Exception as e:
        print(f"❌ Error in speech recognition test: {e}")
        import traceback
        traceback.print_exc()
        return False, str(e)

def main():
    """Main test menu"""
    print("🤖 Matrix AI Simple Speech Test")
    print("=" * 60)
    print("🎯 Choose a test mode:")
    print()
    print("1. 🎤 Single Speech Interaction (Complete flow)")
    print("2. 🔄 Continuous Speech Mode (Multiple interactions)")
    print("3. 🗣️ Speech Recognition Only (No AI response)")
    print("4. 🚀 Quick Test (Single interaction)")
    print()
    
    while True:
        try:
            choice = input("Enter your choice (1-4) or 'q' to quit: ").strip().lower()
            
            if choice == 'q' or choice == 'quit':
                print("👋 Goodbye!")
                break
            elif choice == '1':
                print("\n" + "="*60)
                success = test_single_interaction()
                if success:
                    print("\n🎉 Single interaction test completed successfully!")
                else:
                    print("\n⚠️ Single interaction test had issues")
            elif choice == '2':
                print("\n" + "="*60)
                success = test_continuous_mode()
                if success:
                    print("\n🎉 Continuous mode test completed successfully!")
                else:
                    print("\n⚠️ Continuous mode test had issues")
            elif choice == '3':
                print("\n" + "="*60)
                success, result = test_speech_recognition_only()
                if success:
                    print(f"\n🎉 Speech recognition test successful! Recognized: '{result}'")
                else:
                    print(f"\n⚠️ Speech recognition test failed: {result}")
            elif choice == '4':
                print("\n" + "="*60)
                print("🚀 Quick Test - Single Interaction")
                success = test_single_interaction()
                if success:
                    print("\n🎉 Quick test passed! Speech-to-response is working!")
                else:
                    print("\n⚠️ Quick test failed! Check the error messages above.")
                break
            else:
                print("❌ Invalid choice. Please enter 1-4 or 'q'")
            
            print("\n" + "="*60)
            print("🔄 Choose another test or 'q' to quit:")
            
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()
