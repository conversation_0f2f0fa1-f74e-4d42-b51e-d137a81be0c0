#!/usr/bin/env python3
"""
Comprehensive test script for Matrix AI speech-to-response functionality.
Tests the complete flow: user speaks → speech recognition → AI processing → response generation → text-to-speech output.
"""

import sys
import os
import time
import threading
from datetime import datetime

# Add the current directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_speech_recognition_basic():
    """Test basic speech recognition functionality"""
    print("🎤 Testing Basic Speech Recognition")
    print("=" * 50)
    
    try:
        from Backend.SpeechToText import SpeechRecognition
        from Frontend.GUI import SetMicrophoneStatus, GetMicrophoneStatus, SetAssistantStatus
        
        print("📊 Checking microphone status...")
        original_status = GetMicrophoneStatus()
        print(f"   Original microphone status: {original_status}")
        
        # Ensure microphone is active for testing
        if original_status.lower() != "true":
            print("   Activating microphone for test...")
            SetMicrophoneStatus("True")
        
        print("\n🎤 SPEECH RECOGNITION TEST")
        print("   Please say something clearly when prompted...")
        print("   Try saying: 'Hello Matrix AI' or 'What time is it?'")
        print()
        
        input("Press Enter when ready to start speech recognition...")
        
        print("🗣️ Listening... Please speak now!")
        SetAssistantStatus("Testing - Listening...")
        
        start_time = time.time()
        result = SpeechRecognition()
        end_time = time.time()
        
        print(f"\n📊 SPEECH RECOGNITION RESULTS:")
        print(f"   Recognition time: {end_time - start_time:.2f} seconds")
        print(f"   Recognized text: '{result}'")
        
        # Restore original microphone status
        if original_status.lower() != "true":
            SetMicrophoneStatus(original_status)
            print(f"   Microphone status restored to: {original_status}")
        
        # Evaluate result
        if result and result not in [
            "I didn't hear anything. Please try again.",
            "I'm having trouble with speech recognition. Please try again later.",
            "I didn't catch that.",
            "Error in speech recognition"
        ]:
            print("✅ Speech recognition working correctly")
            return True, result
        else:
            print("❌ Speech recognition failed or no input detected")
            return False, result
        
    except Exception as e:
        print(f"❌ Error in speech recognition test: {e}")
        import traceback
        traceback.print_exc()
        return False, str(e)

def test_ai_processing(test_query):
    """Test AI processing and decision making"""
    print("\n🧠 Testing AI Processing and Decision Making")
    print("=" * 50)
    
    try:
        from Backend.Model import FirstLayerDMM
        from Frontend.GUI import SetAssistantStatus
        
        print(f"📝 Processing query: '{test_query}'")
        SetAssistantStatus("Testing - Thinking...")
        
        start_time = time.time()
        decision = FirstLayerDMM(test_query)
        end_time = time.time()
        
        print(f"\n📊 AI PROCESSING RESULTS:")
        print(f"   Processing time: {end_time - start_time:.2f} seconds")
        print(f"   Decision output: {decision}")
        
        if decision and isinstance(decision, list) and len(decision) > 0:
            print("✅ AI processing working correctly")
            return True, decision
        else:
            print("❌ AI processing failed or returned empty result")
            return False, decision
        
    except Exception as e:
        print(f"❌ Error in AI processing test: {e}")
        import traceback
        traceback.print_exc()
        return False, str(e)

def test_response_generation(decision):
    """Test response generation based on AI decision"""
    print("\n💬 Testing Response Generation")
    print("=" * 50)
    
    try:
        from Backend.Chatbot import ChatBot
        from Backend.RealtimeSearchEngine import RealtimeSearchEngine
        from Backend.DateTime import process_datetime_query
        from Frontend.GUI import SetAssistantStatus, QueryModifier
        
        print(f"📝 Generating response for decision: {decision}")
        SetAssistantStatus("Testing - Generating response...")
        
        response = None
        response_type = "unknown"
        
        start_time = time.time()
        
        # Process based on decision type
        for query in decision:
            if query.startswith("general"):
                response_type = "general"
                query_final = query.replace("general ", "")
                response = ChatBot(QueryModifier(query_final))
                break
            elif query.startswith("realtime"):
                response_type = "realtime"
                query_final = query.replace("realtime ", "")
                response = RealtimeSearchEngine(QueryModifier(query_final))
                break
            elif query.startswith("datetime"):
                response_type = "datetime"
                datetime_query = query.replace("datetime ", "")
                response = process_datetime_query(datetime_query)
                break
        
        end_time = time.time()
        
        print(f"\n📊 RESPONSE GENERATION RESULTS:")
        print(f"   Response type: {response_type}")
        print(f"   Generation time: {end_time - start_time:.2f} seconds")
        print(f"   Response: '{response}'")
        
        if response and len(response.strip()) > 0:
            print("✅ Response generation working correctly")
            return True, response
        else:
            print("❌ Response generation failed or returned empty result")
            return False, response
        
    except Exception as e:
        print(f"❌ Error in response generation test: {e}")
        import traceback
        traceback.print_exc()
        return False, str(e)

def test_text_to_speech(response_text):
    """Test text-to-speech functionality"""
    print("\n🔊 Testing Text-to-Speech")
    print("=" * 50)
    
    try:
        from Backend.TextToSpeech import text_to_speech
        from Frontend.GUI import SetAssistantStatus
        
        print(f"📢 Converting to speech: '{response_text[:100]}{'...' if len(response_text) > 100 else ''}'")
        SetAssistantStatus("Testing - Speaking...")
        
        print("🔊 Starting text-to-speech...")
        print("   You should hear the AI speaking the response.")
        
        start_time = time.time()
        text_to_speech(response_text)
        end_time = time.time()
        
        print(f"\n📊 TEXT-TO-SPEECH RESULTS:")
        print(f"   Speech duration: {end_time - start_time:.2f} seconds")
        print("✅ Text-to-speech completed")
        
        return True
        
    except Exception as e:
        print(f"❌ Error in text-to-speech test: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_complete_flow():
    """Test the complete speech-to-response flow"""
    print("\n🚀 Testing Complete Speech-to-Response Flow")
    print("=" * 60)
    
    try:
        from main import MainExecution
        from Frontend.GUI import SetMicrophoneStatus, GetMicrophoneStatus, SetAssistantStatus
        
        print("🎯 This tests the complete flow as it would work in the actual application")
        print("📋 Flow: Speech Recognition → AI Processing → Response Generation → Text-to-Speech")
        print()
        
        # Ensure microphone is active
        original_status = GetMicrophoneStatus()
        if original_status.lower() != "true":
            print("Activating microphone for complete flow test...")
            SetMicrophoneStatus("True")
        
        print("🎤 COMPLETE FLOW TEST")
        print("   Please say a clear command when prompted...")
        print("   Examples:")
        print("   - 'What time is it?'")
        print("   - 'Hello Matrix AI'")
        print("   - 'Tell me about artificial intelligence'")
        print("   - 'What's the weather today?'")
        print()
        
        input("Press Enter when ready to start the complete flow test...")
        
        print("🚀 Starting complete flow... Please speak now!")
        SetAssistantStatus("Complete Flow Test - Ready")
        
        start_time = time.time()
        
        # Run the main execution (this includes the complete flow)
        result = MainExecution()
        
        end_time = time.time()
        
        print(f"\n📊 COMPLETE FLOW RESULTS:")
        print(f"   Total execution time: {end_time - start_time:.2f} seconds")
        print(f"   Flow completed: {result}")
        
        # Restore original microphone status
        if original_status.lower() != "true":
            SetMicrophoneStatus(original_status)
            print(f"   Microphone status restored to: {original_status}")
        
        if result:
            print("✅ Complete flow working correctly")
            return True
        else:
            print("❌ Complete flow failed")
            return False
        
    except Exception as e:
        print(f"❌ Error in complete flow test: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_multiple_interactions():
    """Test multiple consecutive interactions"""
    print("\n🔄 Testing Multiple Consecutive Interactions")
    print("=" * 50)
    
    try:
        from Frontend.GUI import SetMicrophoneStatus, GetMicrophoneStatus
        
        print("🎯 This tests multiple speech interactions in sequence")
        print("📋 You'll be asked to speak 3 different commands")
        print()
        
        # Ensure microphone is active
        original_status = GetMicrophoneStatus()
        if original_status.lower() != "true":
            SetMicrophoneStatus("True")
        
        test_commands = [
            "What time is it?",
            "Hello Matrix AI",
            "Tell me a joke"
        ]
        
        results = []
        
        for i, suggested_command in enumerate(test_commands, 1):
            print(f"\n🎤 INTERACTION {i}/3")
            print(f"   Suggested command: '{suggested_command}'")
            print("   (You can say this or any other command)")
            
            input(f"Press Enter when ready for interaction {i}...")
            
            # Test the complete flow for this interaction
            try:
                from main import MainExecution
                result = MainExecution()
                results.append(result)
                print(f"   ✅ Interaction {i} completed: {result}")
            except Exception as e:
                print(f"   ❌ Interaction {i} failed: {e}")
                results.append(False)
            
            if i < len(test_commands):
                print("   Waiting 2 seconds before next interaction...")
                time.sleep(2)
        
        # Restore original microphone status
        if original_status.lower() != "true":
            SetMicrophoneStatus(original_status)
        
        successful_interactions = sum(1 for r in results if r)
        
        print(f"\n📊 MULTIPLE INTERACTIONS RESULTS:")
        print(f"   Successful interactions: {successful_interactions}/{len(results)}")
        print(f"   Success rate: {(successful_interactions/len(results)*100):.1f}%")
        
        if successful_interactions >= len(results) * 0.7:  # 70% success rate
            print("✅ Multiple interactions working well")
            return True
        else:
            print("❌ Multiple interactions need improvement")
            return False
        
    except Exception as e:
        print(f"❌ Error in multiple interactions test: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function"""
    print("🤖 Matrix AI Speech-to-Response Comprehensive Test")
    print("=" * 70)
    print(f"🕒 Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    print("📋 Test Overview:")
    print("   1. Basic Speech Recognition")
    print("   2. AI Processing & Decision Making")
    print("   3. Response Generation")
    print("   4. Text-to-Speech Output")
    print("   5. Complete Flow Integration")
    print("   6. Multiple Consecutive Interactions")
    print()
    
    # Initialize results tracking
    test_results = {}
    
    # Test 1: Basic Speech Recognition
    print("🧪 TEST 1: Basic Speech Recognition")
    print("-" * 40)
    speech_success, speech_result = test_speech_recognition_basic()
    test_results['speech_recognition'] = speech_success
    
    if speech_success:
        # Test 2: AI Processing (using the recognized speech)
        print("\n🧪 TEST 2: AI Processing")
        print("-" * 40)
        ai_success, ai_decision = test_ai_processing(speech_result)
        test_results['ai_processing'] = ai_success
        
        if ai_success:
            # Test 3: Response Generation
            print("\n🧪 TEST 3: Response Generation")
            print("-" * 40)
            response_success, response_text = test_response_generation(ai_decision)
            test_results['response_generation'] = response_success
            
            if response_success:
                # Test 4: Text-to-Speech
                print("\n🧪 TEST 4: Text-to-Speech")
                print("-" * 40)
                tts_success = test_text_to_speech(response_text)
                test_results['text_to_speech'] = tts_success
    
    # Test 5: Complete Flow (independent test)
    print("\n🧪 TEST 5: Complete Flow Integration")
    print("-" * 40)
    complete_success = test_complete_flow()
    test_results['complete_flow'] = complete_success
    
    # Test 6: Multiple Interactions (optional)
    print("\n🧪 TEST 6: Multiple Consecutive Interactions")
    print("-" * 40)
    user_choice = input("Do you want to test multiple interactions? (y/n): ").lower().strip()
    
    if user_choice in ['y', 'yes']:
        multiple_success = test_multiple_interactions()
        test_results['multiple_interactions'] = multiple_success
    else:
        print("Skipping multiple interactions test")
        test_results['multiple_interactions'] = None
    
    # Final Summary
    print("\n" + "=" * 70)
    print("📊 COMPREHENSIVE TEST SUMMARY")
    print("=" * 70)
    
    for test_name, result in test_results.items():
        if result is None:
            status = "⏭️ SKIPPED"
        elif result:
            status = "✅ PASSED"
        else:
            status = "❌ FAILED"
        
        test_display = test_name.replace('_', ' ').title()
        print(f"{test_display:.<30} {status}")
    
    # Calculate overall success
    completed_tests = [r for r in test_results.values() if r is not None]
    passed_tests = [r for r in completed_tests if r]
    
    if completed_tests:
        success_rate = (len(passed_tests) / len(completed_tests)) * 100
        print(f"\nOverall Success Rate: {success_rate:.1f}% ({len(passed_tests)}/{len(completed_tests)} tests passed)")
        
        if success_rate >= 80:
            print("\n🎉 EXCELLENT! Speech-to-response system is working very well!")
        elif success_rate >= 60:
            print("\n👍 GOOD! Speech-to-response system is mostly working!")
        else:
            print("\n⚠️ NEEDS IMPROVEMENT! Several components need attention!")
    
    print(f"\n🕒 Test completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("\n🎯 Test finished! Check the results above for any issues.")

if __name__ == "__main__":
    main()
