import sys
import os
import math
import random
from PyQt5.QtWidgets import <PERSON>A<PERSON><PERSON>, QWidget, QVBoxLayout, QLabel
from PyQt5.QtGui import QPainter, QColor, QPen, QBrush, QPainterPath, QRadialGradient, QLinearGradient
from PyQt5.QtCore import Qt, QTimer, QPointF, QRectF, pyqtSignal, QSize, QPropertyAnimation, QEasingCurve

class VoiceAssistantAnimation(QWidget):
    """
    An enhanced circular animation similar to <PERSON><PERSON> with lines that respond to sound.
    Features multiple animation layers, particle effects, and dynamic color transitions.
    The animation changes based on the assistant's state (listening, speaking, idle).
    """

    def __init__(self, parent=None, size=300):
        super().__init__(parent)
        self.setMinimumSize(size, size)
        self.setMaximumSize(size, size)

        # Animation parameters
        self.num_lines = 80  # Increased number of lines for more detail
        self.base_radius = size // 2 - 30  # Base radius of the circle
        self.min_line_length = 10  # Minimum line length
        self.max_line_length = 50  # Maximum line length when active
        self.line_width_range = (1, 5)  # Dynamic line width range

        # Central circle parameters
        self.center_circle_size = size // 6
        self.center_pulse_amount = 0.2  # How much the center circle pulses
        self.center_pulse_speed = 0.05  # Speed of center circle pulsing

        # Particle effect parameters
        self.num_particles = 30
        self.particles = []
        self.init_particles()

        # Wave effect parameters
        self.wave_radius = self.base_radius * 1.2
        self.wave_width = 5
        self.wave_opacity = 0
        self.max_wave_opacity = 150  # Max opacity for wave effect

        # Animation state
        self.mode = "idle"  # Can be "idle", "listening", or "speaking"
        self.amplitudes = [self.min_line_length] * self.num_lines  # Current amplitude of each line
        self.target_amplitudes = [self.min_line_length] * self.num_lines  # Target amplitude for smooth animation
        self.line_widths = [random.uniform(*self.line_width_range) for _ in range(self.num_lines)]  # Width of each line
        self.animation_speed = 0.2  # Speed of amplitude change (0-1)
        self.time_offset = 0  # For animation timing

        # Enhanced color scheme
        self.idle_primary = QColor(70, 130, 230)  # Primary blue
        self.idle_secondary = QColor(100, 180, 255)  # Secondary blue
        self.idle_accent = QColor(150, 200, 255)  # Accent blue

        self.listening_primary = QColor(50, 200, 100)  # Primary green
        self.listening_secondary = QColor(100, 230, 150)  # Secondary green
        self.listening_accent = QColor(150, 255, 200)  # Accent green

        self.speaking_primary = QColor(230, 80, 80)  # Primary red
        self.speaking_secondary = QColor(255, 120, 120)  # Secondary red
        self.speaking_accent = QColor(255, 160, 160)  # Accent red

        # Current colors
        self.current_primary = self.idle_primary
        self.current_secondary = self.idle_secondary
        self.current_accent = self.idle_accent

        # Target colors for transitions
        self.target_primary = self.idle_primary
        self.target_secondary = self.idle_secondary
        self.target_accent = self.idle_accent

        self.color_transition = 0.0  # For smooth color transitions

        # Start the animation timer
        self.timer = QTimer(self)
        self.timer.timeout.connect(self.update_animation)
        self.timer.start(16)  # ~60 FPS

        # Path to status file
        self.current_dir = os.getcwd()
        self.temp_dir_path = os.path.join(self.current_dir, "Frontend", "Files")
        self.status_file = os.path.join(self.temp_dir_path, "Status.data")

        # Timer to check assistant status
        self.status_timer = QTimer(self)
        self.status_timer.timeout.connect(self.check_assistant_status)
        self.status_timer.start(100)  # Check status every 100ms

    def init_particles(self):
        """Initialize particle effects around the main animation."""
        self.particles = []
        center_x = self.width() // 2
        center_y = self.height() // 2

        for _ in range(self.num_particles):
            # Random angle and distance from center
            angle = random.uniform(0, 2 * math.pi)
            distance = random.uniform(0.5, 0.9) * self.base_radius

            # Position
            x = center_x + distance * math.cos(angle)
            y = center_y + distance * math.sin(angle)

            # Velocity (slow movement in random directions)
            vx = random.uniform(-0.5, 0.5)
            vy = random.uniform(-0.5, 0.5)

            # Size and opacity
            size = random.uniform(2, 6)
            opacity = random.randint(50, 200)

            # Add particle
            self.particles.append({
                'x': x, 'y': y,
                'vx': vx, 'vy': vy,
                'size': size,
                'opacity': opacity,
                'max_opacity': opacity,
                'life': random.uniform(0.7, 1.0)  # Life factor (1.0 = full life)
            })

    def check_assistant_status(self):
        """Check the assistant's status from the Status.data file and update animation mode."""
        try:
            if os.path.exists(self.status_file):
                with open(self.status_file, "r", encoding="utf-8") as file:
                    status = file.read().strip().lower()

                if "listening" in status:
                    self.set_mode("listening")
                elif "speaking" in status or "generating" in status:
                    self.set_mode("speaking")
                else:
                    self.set_mode("idle")
        except Exception as e:
            print(f"Error reading status file: {e}")

    def set_mode(self, mode):
        """Set the animation mode and update colors with smooth transitions."""
        if self.mode != mode:
            self.mode = mode
            self.color_transition = 0.0  # Reset transition progress

            # Trigger a wave effect on mode change
            self.wave_opacity = self.max_wave_opacity

            # Reset particles with new colors
            self.init_particles()

            # Update animation parameters based on mode
            if mode == "idle":
                self.target_primary = self.idle_primary
                self.target_secondary = self.idle_secondary
                self.target_accent = self.idle_accent
                self.animation_speed = 0.15  # Slower, more gentle animation
            elif mode == "listening":
                self.target_primary = self.listening_primary
                self.target_secondary = self.listening_secondary
                self.target_accent = self.listening_accent
                self.animation_speed = 0.25  # Medium speed animation
            elif mode == "speaking":
                self.target_primary = self.speaking_primary
                self.target_secondary = self.speaking_secondary
                self.target_accent = self.speaking_accent
                self.animation_speed = 0.35  # Faster, more energetic animation

    def update_animation(self):
        """Update the animation state for the next frame."""
        # Update time offset for animations
        self.time_offset += 0.05

        # Update color transition
        if self.color_transition < 1.0:
            self.color_transition += 0.05  # Transition speed
            # Smoothly interpolate between colors
            self.current_primary = self.interpolate_color(
                self.current_primary, self.target_primary, self.color_transition)
            self.current_secondary = self.interpolate_color(
                self.current_secondary, self.target_secondary, self.color_transition)
            self.current_accent = self.interpolate_color(
                self.current_accent, self.target_accent, self.color_transition)

        # Update wave effect
        if self.wave_opacity > 0:
            self.wave_opacity -= 3  # Fade out speed
            self.wave_radius += 1.5  # Expand speed

        # Update particles
        self.update_particles()

        # Mode-specific line animations
        if self.mode == "idle":
            # Gentle pulsing in idle mode with multiple wave patterns
            base_amplitude = self.min_line_length + (self.max_line_length - self.min_line_length) * 0.3
            for i in range(self.num_lines):
                angle = 2 * math.pi * i / self.num_lines
                # Combine multiple sine waves for more complex motion
                wave1 = 0.1 * math.sin(angle * 2 + self.time_offset * 0.5)
                wave2 = 0.05 * math.sin(angle * 4 + self.time_offset * 0.3)
                pulse_factor = wave1 + wave2
                self.target_amplitudes[i] = base_amplitude + pulse_factor * self.max_line_length

                # Slowly vary line widths
                self.line_widths[i] = self.line_width_range[0] + (
                    math.sin(angle * 3 + self.time_offset * 0.2) * 0.5 + 0.5
                ) * (self.line_width_range[1] - self.line_width_range[0])

        elif self.mode == "listening":
            # Enhanced wave pattern when listening
            for i in range(self.num_lines):
                angle = 2 * math.pi * i / self.num_lines
                # Create multiple overlapping wave patterns
                time_factor = self.time_offset
                wave1 = math.sin(angle * 3 + time_factor) * 0.5 + 0.5
                wave2 = math.sin(angle * 5 - time_factor * 0.7) * 0.3 + 0.5
                wave = (wave1 + wave2) / 1.5  # Combine waves and normalize
                self.target_amplitudes[i] = self.min_line_length + wave * (self.max_line_length - self.min_line_length)

                # Dynamic line widths based on amplitude
                self.line_widths[i] = self.line_width_range[0] + wave * (
                    self.line_width_range[1] - self.line_width_range[0])

        elif self.mode == "speaking":
            # More energetic, random movement when speaking
            for i in range(self.num_lines):
                angle = 2 * math.pi * i / self.num_lines

                # Random heights with coherence between adjacent lines
                if random.random() < 0.15:  # Occasionally update target
                    # Add some coherence with adjacent lines
                    prev_i = (i - 1) % self.num_lines
                    next_i = (i + 1) % self.num_lines
                    avg_adjacent = (self.target_amplitudes[prev_i] + self.target_amplitudes[next_i]) / 2

                    # Mix random value with adjacent average for smoother transitions
                    rand_val = random.random() * 0.7 + (avg_adjacent / self.max_line_length) * 0.3
                    self.target_amplitudes[i] = self.min_line_length + rand_val * (self.max_line_length - self.min_line_length)

                # More dynamic line widths
                if random.random() < 0.1:
                    self.line_widths[i] = random.uniform(*self.line_width_range)

        # Smoothly animate current amplitudes toward target amplitudes
        for i in range(self.num_lines):
            self.amplitudes[i] += (self.target_amplitudes[i] - self.amplitudes[i]) * self.animation_speed

        # Request a repaint
        self.update()

    def update_particles(self):
        """Update particle positions and properties."""
        center_x = self.width() // 2
        center_y = self.height() // 2

        # Update existing particles
        for particle in self.particles[:]:
            # Update position
            particle['x'] += particle['vx']
            particle['y'] += particle['vy']

            # Slowly decrease life
            particle['life'] -= 0.005

            # Update opacity based on life
            particle['opacity'] = int(particle['max_opacity'] * particle['life'])

            # Remove dead particles
            if particle['life'] <= 0:
                self.particles.remove(particle)

        # Add new particles to replace dead ones
        while len(self.particles) < self.num_particles:
            # Random angle and distance from center
            angle = random.uniform(0, 2 * math.pi)
            distance = random.uniform(0.5, 0.9) * self.base_radius

            # Position
            x = center_x + distance * math.cos(angle)
            y = center_y + distance * math.sin(angle)

            # Velocity (slow movement in random directions)
            vx = random.uniform(-0.5, 0.5)
            vy = random.uniform(-0.5, 0.5)

            # Size and opacity
            size = random.uniform(2, 6)
            opacity = random.randint(50, 200)

            # Add particle
            self.particles.append({
                'x': x, 'y': y,
                'vx': vx, 'vy': vy,
                'size': size,
                'opacity': opacity,
                'max_opacity': opacity,
                'life': random.uniform(0.7, 1.0)  # Life factor (1.0 = full life)
            })

    def interpolate_color(self, color1, color2, factor):
        """Interpolate between two colors."""
        r = int(color1.red() + (color2.red() - color1.red()) * factor)
        g = int(color1.green() + (color2.green() - color1.green()) * factor)
        b = int(color1.blue() + (color2.blue() - color1.blue()) * factor)
        a = int(color1.alpha() + (color2.alpha() - color1.alpha()) * factor)
        return QColor(r, g, b, a)

    def paintEvent(self, _):
        """Paint the animation with enhanced visual effects."""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing, True)

        # Calculate center point
        center_x = self.width() // 2
        center_y = self.height() // 2

        # Draw expanding wave effect (when mode changes)
        if self.wave_opacity > 0:
            wave_color = QColor(self.current_accent)
            wave_color.setAlpha(self.wave_opacity)
            pen = QPen(wave_color)
            pen.setWidth(self.wave_width)
            painter.setPen(pen)
            painter.setBrush(Qt.NoBrush)
            painter.drawEllipse(
                int(center_x - self.wave_radius),
                int(center_y - self.wave_radius),
                int(self.wave_radius * 2),
                int(self.wave_radius * 2)
            )

        # Draw particles
        for particle in self.particles:
            particle_color = QColor(self.current_accent)
            particle_color.setAlpha(particle['opacity'])
            painter.setBrush(QBrush(particle_color))
            painter.setPen(Qt.NoPen)
            painter.drawEllipse(
                int(particle['x'] - particle['size'] / 2),
                int(particle['y'] - particle['size'] / 2),
                int(particle['size']),
                int(particle['size'])
            )

        # Draw central glow
        center_size = self.center_circle_size * (1 + math.sin(self.time_offset * self.center_pulse_speed) * self.center_pulse_amount)

        # Create radial gradient for center glow
        gradient = QRadialGradient(center_x, center_y, center_size)
        gradient.setColorAt(0, self.current_accent)
        gradient.setColorAt(0.7, self.current_secondary)
        gradient.setColorAt(1, QColor(self.current_primary.red(), self.current_primary.green(),
                                     self.current_primary.blue(), 0))

        painter.setBrush(QBrush(gradient))
        painter.setPen(Qt.NoPen)
        painter.drawEllipse(
            int(center_x - center_size),
            int(center_y - center_size),
            int(center_size * 2),
            int(center_size * 2)
        )

        # Draw the lines with gradient effect
        for i in range(self.num_lines):
            # Calculate angle for this line
            angle = 2 * math.pi * i / self.num_lines

            # Calculate start and end points
            inner_radius = self.base_radius - self.amplitudes[i]
            outer_radius = self.base_radius + self.amplitudes[i]

            start_x = center_x + inner_radius * math.cos(angle)
            start_y = center_y + inner_radius * math.sin(angle)

            end_x = center_x + outer_radius * math.cos(angle)
            end_y = center_y + outer_radius * math.sin(angle)

            # Create gradient for this line
            line_gradient = QLinearGradient(start_x, start_y, end_x, end_y)
            line_gradient.setColorAt(0, self.current_secondary)
            line_gradient.setColorAt(1, self.current_primary)

            # Set up the pen with gradient and dynamic width
            pen = QPen(QBrush(line_gradient), self.line_widths[i])
            pen.setCapStyle(Qt.RoundCap)
            painter.setPen(pen)

            # Draw the line
            painter.drawLine(int(start_x), int(start_y), int(end_x), int(end_y))

        # Draw central circle with gradient
        center_gradient = QRadialGradient(center_x, center_y, self.center_circle_size / 2)
        center_gradient.setColorAt(0, self.current_accent)
        center_gradient.setColorAt(1, self.current_secondary)

        painter.setBrush(QBrush(center_gradient))
        painter.setPen(Qt.NoPen)
        painter.drawEllipse(
            int(center_x - self.center_circle_size / 2),
            int(center_y - self.center_circle_size / 2),
            int(self.center_circle_size),
            int(self.center_circle_size)
        )

    def sizeHint(self):
        """Return the preferred size of the widget."""
        return QSize(300, 300)

# For testing the animation standalone
if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = QWidget()
    layout = QVBoxLayout(window)

    animation = VoiceAssistantAnimation()
    layout.addWidget(animation, alignment=Qt.AlignCenter)

    window.setStyleSheet("background-color: black;")
    window.setGeometry(100, 100, 400, 400)
    window.show()

    # Test different modes
    animation.set_mode("idle")
    QTimer.singleShot(3000, lambda: animation.set_mode("listening"))
    QTimer.singleShot(6000, lambda: animation.set_mode("speaking"))
    QTimer.singleShot(9000, lambda: animation.set_mode("idle"))

    sys.exit(app.exec_())
