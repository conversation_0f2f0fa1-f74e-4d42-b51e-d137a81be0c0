# Matrix AI TO-DO List Feature Documentation

## Overview
The Matrix AI TO-DO list feature provides comprehensive task management through voice commands. Users can add, edit, remove, complete, and list tasks with deadlines and automatic reminders.

## Features

### 1. Task Management
- **Add Tasks**: Create new tasks with descriptions, deadlines, and reminder times
- **List Tasks**: View all pending and completed tasks
- **Edit Tasks**: Modify task descriptions, deadlines, and other details
- **Complete Tasks**: Mark tasks as finished
- **Remove Tasks**: Delete tasks from the list
- **Task Summary**: Get an overview of all tasks with statistics

### 2. Smart Date/Time Parsing
- **Relative Dates**: "today", "tomorrow", "next week", "next month"
- **Day Names**: "monday", "tuesday", etc. (automatically sets to next occurrence)
- **Natural Language**: "friday at 3pm", "25th june at 9:00pm"
- **Flexible Formats**: Supports various date and time formats

### 3. Automatic Reminders
- **Background Monitoring**: Continuous checking for due reminders
- **Voice Notifications**: Speaks reminders at specified times
- **Visual Alerts**: Shows reminders on screen
- **Smart Timing**: 1-minute window for reminder accuracy

### 4. Priority Levels
- **High Priority**: 🔴 Critical tasks
- **Medium Priority**: 🟡 Normal tasks (default)
- **Low Priority**: 🟢 Optional tasks

## Voice Commands

### Adding Tasks
```
"remind me to call john at 5pm tomorrow"
"add task buy groceries deadline friday"
"set a todo to finish project by monday remind me on sunday"
"remind me to take medicine at 8am daily"
```

### Listing Tasks
```
"show my tasks"
"list my todos"
"what are my pending tasks"
"display my todo list"
```

### Removing Tasks
```
"remove task 1"
"delete buy groceries task"
"cancel my meeting reminder"
```

### Editing Tasks
```
"edit task 1 change deadline to friday"
"update my meeting time to 3pm"
"modify grocery task add milk"
```

### Completing Tasks
```
"mark task 1 as complete"
"finish grocery task"
"task done buy milk"
```

### Task Summary
```
"show task summary"
"todo overview"
"task status"
```

## Technical Implementation

### Files Created/Modified
1. **Backend/TodoManager.py** - Core TO-DO management system
2. **Data/TodoList.json** - Task storage file
3. **Backend/Model.py** - Added TO-DO functions to AI model
4. **main.py** - Integrated TO-DO functionality
5. **Requirements.txt** - Added python-dateutil dependency

### Key Classes and Functions

#### TodoManager Class
- `add_task()` - Add new tasks
- `list_tasks()` - Display tasks
- `remove_task()` - Delete tasks
- `complete_task()` - Mark as completed
- `edit_task()` - Modify tasks
- `get_task_summary()` - Statistics overview
- `start_reminder_system()` - Background reminders

#### Voice Processing
- `process_todo_command()` - Parse voice commands
- Smart command parsing for natural language input

### Data Structure
```json
{
  "id": 1,
  "description": "Buy groceries",
  "deadline": "2024-01-26T18:00:00",
  "remind_time": "2024-01-26T17:00:00",
  "priority": "medium",
  "completed": false,
  "created_at": "2024-01-25T10:30:00",
  "completed_at": null,
  "reminder_sent": false
}
```

## Usage Examples

### Example 1: Simple Reminder
**User**: "remind me to call mom at 6pm today"
**Matrix**: "Task added successfully: 'call mom' with deadline on Friday, January 26 at 06:00 PM and reminder set for Friday, January 26 at 05:00 PM"

### Example 2: Task with Deadline
**User**: "add task submit report deadline monday"
**Matrix**: "Task added successfully: 'submit report' with deadline on Monday, January 29 at 06:00 PM and reminder set for Monday, January 29 at 05:00 PM"

### Example 3: List Tasks
**User**: "show my tasks"
**Matrix**: "Your pending tasks:
1. 🟡 call mom (Due: Jan 26, 06:00 PM)
2. 🟡 submit report (Due: Jan 29, 06:00 PM)"

### Example 4: Complete Task
**User**: "mark task 1 as complete"
**Matrix**: "Task completed: 'call mom'. Well done!"

## Error Handling
- Invalid date formats are handled gracefully
- Missing tasks return helpful error messages
- Speech recognition failures are managed
- File I/O errors are caught and reported

## Background Services
- **Reminder Thread**: Runs continuously checking for due reminders
- **Auto-save**: Tasks are automatically saved to JSON file
- **Cross-platform**: Works on Windows, macOS, and Linux

## Integration with Matrix AI
- Seamlessly integrated with existing voice command system
- Uses same text-to-speech and GUI display functions
- Follows Matrix AI's response patterns and status updates
- Compatible with all existing Matrix AI features

## Future Enhancements
- Recurring tasks (daily, weekly, monthly)
- Task categories and tags
- Export/import functionality
- Integration with calendar applications
- Collaborative task sharing
