#!/usr/bin/env python3
"""
Test script for Matrix AI email automation functionality.
This script tests the enhanced SendEmail function with browser automation.
"""

import sys
import os
import time

# Add the current directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_email_automation():
    """Test the email automation functionality"""
    print("=" * 60)
    print("🧪 MATRIX AI EMAIL AUTOMATION TEST")
    print("=" * 60)
    
    try:
        # Import the email functions
        from Backend.Automation import SendEmail, validate_email, extract_email_from_text
        
        print("✅ Successfully imported email functions")
        
        # Test email validation
        print("\n📧 Testing email validation...")
        test_emails = [
            "<EMAIL>",
            "<EMAIL>", 
            "invalid-email",
            "<EMAIL>"
        ]
        
        for email in test_emails:
            is_valid = validate_email(email)
            status = "✅ Valid" if is_valid else "❌ Invalid"
            print(f"   {email}: {status}")
        
        # Test email extraction
        print("\n🔍 Testing email extraction...")
        test_texts = [
            "Send <NAME_EMAIL> please",
            "My <NAME_EMAIL>",
            "Contact <EMAIL> for help",
            "No email in this text"
        ]
        
        for text in test_texts:
            extracted = extract_email_from_text(text)
            result = extracted if extracted else "No email found"
            print(f"   '{text}' -> {result}")
        
        # Test the complete email flow (without actually sending)
        print("\n📤 Testing email compose functionality...")
        
        # Test with a safe email address (you can change this to your own)
        test_email = "<EMAIL>"
        test_subject = "Matrix AI Test Email"
        test_body = "This is a test email from Matrix AI Assistant."
        
        print(f"   Recipient: {test_email}")
        print(f"   Subject: {test_subject}")
        print(f"   Body: {test_body}")
        
        # Call the SendEmail function
        print("\n🚀 Calling SendEmail function...")
        success, message = SendEmail(test_email, test_subject, test_body)
        
        print(f"   Success: {success}")
        print(f"   Message: {message}")
        
        if success:
            print("\n✅ Email automation test completed successfully!")
            print("📝 Note: Gmail compose window should have opened in your browser.")
            if "sent successfully" in message.lower():
                print("🎉 Email was automatically sent!")
            else:
                print("📋 Email compose window opened. Manual send required.")
        else:
            print(f"\n❌ Email automation test failed: {message}")
        
        return success
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("🔧 Make sure all required modules are installed:")
        print("   - selenium")
        print("   - webdriver-manager")
        print("   - dotenv")
        return False
        
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_webdriver_availability():
    """Test if WebDriver is available for automation"""
    print("\n🌐 Testing WebDriver availability...")
    
    try:
        from Backend.SpeechToText import get_webdriver
        
        print("   Attempting to get WebDriver instance...")
        driver = get_webdriver()
        
        if driver:
            print("   ✅ WebDriver is available and working")
            print(f"   Browser: {driver.name}")
            print(f"   Version: {driver.capabilities.get('browserVersion', 'Unknown')}")
            return True
        else:
            print("   ❌ WebDriver is not available")
            return False
            
    except Exception as e:
        print(f"   ❌ WebDriver test failed: {e}")
        return False

def main():
    """Main test function"""
    print("🤖 Starting Matrix AI Email Automation Tests...\n")
    
    # Test WebDriver availability first
    webdriver_ok = test_webdriver_availability()
    
    # Test email automation
    email_ok = test_email_automation()
    
    print("\n" + "=" * 60)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 60)
    print(f"WebDriver Available: {'✅ Yes' if webdriver_ok else '❌ No'}")
    print(f"Email Automation: {'✅ Working' if email_ok else '❌ Failed'}")
    
    if webdriver_ok and email_ok:
        print("\n🎉 All tests passed! Email automation is ready to use.")
        print("\n📋 Usage Instructions:")
        print("1. Say: 'send mail to [email address]'")
        print("2. Provide subject when asked")
        print("3. Provide email content when asked")
        print("4. Email will be automatically sent (if AUTO_SEND_EMAILS=True)")
    else:
        print("\n⚠️ Some tests failed. Please check the errors above.")
        if not webdriver_ok:
            print("🔧 WebDriver issues may prevent automatic email sending.")
        if not email_ok:
            print("🔧 Email automation may not work properly.")
    
    print("\n🎯 Test completed!")

if __name__ == "__main__":
    main()
