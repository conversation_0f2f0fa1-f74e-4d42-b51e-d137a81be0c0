# Matrix AI Virtual Assistant

Matrix is an advanced AI virtual assistant with multiple capabilities including speech recognition, text-to-speech, real-time information retrieval, and image generation. As a voice-based assistant, <PERSON> can efficiently multitask, handling multiple commands simultaneously and providing verbal feedback about ongoing processes.

## Features

### Speech Recognition
- Browser-based speech recognition using Web Speech API
- Automatic language detection and translation

### Text-to-Speech
- High-quality voice synthesis using Edge TTS
- Customizable voice settings

### Decision Making
- Intelligent query classification using <PERSON>here's command-r-plus model
- Categorizes queries into general, real-time, or task-based requests

### Task Automation
- Open/close applications and websites
- Play music and videos
- Control system settings
- Set reminders

### Information Retrieval
- General knowledge queries handled by AI chatbot
- Real-time information retrieval for current events and data

### Image Generation
- Generate images using the "generate image [prompt]" command
- Uses Hugging Face's Stable Diffusion XL model
- Creates multiple variations of the requested image
- Provides verbal feedback about the image generation process

### Multitasking
- Handle multiple tasks simultaneously
- Generate images while continuing to respond to other commands
- Open multiple applications in parallel
- Provide status updates on background processes
- Seamlessly switch between different types of requests

## Usage Examples

### General Conversation
- "How are you today?"
- "Tell me a joke"
- "What is the capital of France?"

### Real-time Information
- "What's the latest news about climate change?"
- "Who is the current president of the United States?"
- "What's happening in technology today?"

### Application Control
- "Open Chrome"
- "Close Notepad"
- "Open Facebook and Twitter"

### Media Playback
- "Play Bohemian Rhapsody"
- "Play videos about cooking"

### Image Generation
- "Generate image of a sunset over mountains"
- "Generate image of a futuristic city with flying cars"
- "Generate image of a cat wearing a space suit"

### Web Searches
- "Google search for best restaurants near me"
- "YouTube search for guitar tutorials"

### Multitasking Examples
- "Generate image of a mountain landscape and open Notepad"
- "Play Bohemian Rhapsody and search for its lyrics"
- "Open Chrome and Facebook while generating an image of a sunset"
- "Generate image of a cat and tell me a joke while it's processing"

## Setup

1. Install required dependencies
2. Configure API keys in the .env file
3. Run main.py to start the assistant

## Requirements

- Python 3.8+
- Chrome browser (for speech recognition)
- Internet connection for API access
